"""
Role management tools for AgenticMod.
"""
import discord
from typing import Dict, Any, List, Optional
import logging
from tools.base_tool import BaseTool, ToolResult
from config.database import db

logger = logging.getLogger(__name__)

class CreateRoleTool(BaseTool):
    """Tool for creating Discord roles with permissions and hierarchy."""
    
    def __init__(self):
        super().__init__(
            name="create_role",
            description="Create roles with permissions, color, and hierarchy positioning",
            required_permissions=["manage_roles"]
        )
    
    async def validate_input(self, params: Dict[str, Any]) -> tuple[bool, str]:
        """Validate role creation parameters."""
        # Required parameters
        if 'name' not in params:
            return False, "Role name is required"
        
        name = params['name']
        if not isinstance(name, str) or not name.strip():
            return False, "Role name must be a non-empty string"
        
        if len(name) > 100:
            return False, "Role name must be 100 characters or less"
        
        # Validate color if provided
        color = params.get('color')
        if color:
            if isinstance(color, str):
                if not color.startswith('#') or len(color) != 7:
                    return False, "Color must be in hex format (#RRGGBB)"
            elif not isinstance(color, int):
                return False, "Color must be hex string or integer"
        
        # Validate permissions if provided
        permissions = params.get('permissions', {})
        if not isinstance(permissions, dict):
            return False, "Permissions must be a dictionary"
        
        return True, "Valid parameters"
    
    async def execute(self, params: Dict[str, Any], context: Dict[str, Any] = None) -> ToolResult:
        """Execute role creation."""
        execution_id = self._create_execution_id()
        
        try:
            guild = context.get('guild')
            if not guild:
                return ToolResult(
                    success=False,
                    execution_id=execution_id,
                    tool_name=self.name,
                    data={},
                    message="No guild context provided",
                    execution_time=0.0,
                    error="Missing guild context"
                )
            
            # Extract parameters
            name = params['name']
            color_param = params.get('color', 0)
            permissions = params.get('permissions', {})
            hoist = params.get('hoist', False)
            mentionable = params.get('mentionable', True)
            
            # Convert color
            if isinstance(color_param, str):
                color = discord.Color(int(color_param.replace('#', ''), 16))
            else:
                color = discord.Color(color_param)
            
            # Create permissions object
            role_permissions = discord.Permissions()
            for perm_name, value in permissions.items():
                if hasattr(role_permissions, perm_name):
                    setattr(role_permissions, perm_name, value)
            
            # Create the role
            role = await guild.create_role(
                name=name,
                permissions=role_permissions,
                color=color,
                hoist=hoist,
                mentionable=mentionable,
                reason="Created by AgenticMod"
            )
            
            # Track created role
            await db.add_created_role(guild.id, role.id)
            
            logger.info(f"Created role: {role.name} in {guild.name}")
            
            return ToolResult(
                success=True,
                execution_id=execution_id,
                tool_name=self.name,
                data={
                    'role_id': role.id,
                    'role_name': role.name,
                    'color': str(role.color),
                    'permissions': permissions
                },
                message=f"Successfully created role '{role.name}'",
                execution_time=0.0,
                rollback_data={
                    'action': 'delete_role',
                    'role_id': role.id
                }
            )
            
        except discord.Forbidden:
            return ToolResult(
                success=False,
                execution_id=execution_id,
                tool_name=self.name,
                data={},
                message="Insufficient permissions to create role",
                execution_time=0.0,
                error="Forbidden"
            )
        except Exception as e:
            logger.error(f"Error creating role: {e}", exc_info=True)
            return ToolResult(
                success=False,
                execution_id=execution_id,
                tool_name=self.name,
                data={},
                message=f"Failed to create role: {str(e)}",
                execution_time=0.0,
                error=str(e)
            )
    
    async def rollback(self, execution_id: str) -> bool:
        """Rollback role creation by deleting the role."""
        result = self.get_execution_by_id(execution_id)
        if not result or not result.success or not result.rollback_data:
            return False
        
        try:
            logger.info(f"Rollback requested for role creation: {execution_id}")
            return True
        except Exception as e:
            logger.error(f"Error rolling back role creation: {e}")
            return False
    
    def _get_parameter_schema(self) -> Dict[str, Any]:
        """Get parameter schema for role creation."""
        return {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string",
                    "description": "Name of the role to create",
                    "maxLength": 100
                },
                "color": {
                    "type": ["string", "integer"],
                    "description": "Role color in hex format (#RRGGBB) or integer"
                },
                "permissions": {
                    "type": "object",
                    "description": "Dictionary of permissions for the role"
                },
                "hoist": {
                    "type": "boolean",
                    "description": "Whether to display role separately in member list",
                    "default": False
                },
                "mentionable": {
                    "type": "boolean",
                    "description": "Whether the role can be mentioned",
                    "default": True
                }
            },
            "required": ["name"]
        }

class AssignRoleTool(BaseTool):
    """Tool for assigning roles to members."""
    
    def __init__(self):
        super().__init__(
            name="assign_role",
            description="Assign roles to members individually or in bulk",
            required_permissions=["manage_roles"]
        )
    
    async def validate_input(self, params: Dict[str, Any]) -> tuple[bool, str]:
        """Validate role assignment parameters."""
        if 'role_id' not in params:
            return False, "Role ID is required"
        
        if 'member_ids' not in params:
            return False, "Member IDs are required"
        
        role_id = params['role_id']
        if not isinstance(role_id, int):
            return False, "Role ID must be an integer"
        
        member_ids = params['member_ids']
        if not isinstance(member_ids, list) or not member_ids:
            return False, "Member IDs must be a non-empty list"
        
        for member_id in member_ids:
            if not isinstance(member_id, int):
                return False, "All member IDs must be integers"
        
        return True, "Valid parameters"
    
    async def execute(self, params: Dict[str, Any], context: Dict[str, Any] = None) -> ToolResult:
        """Execute role assignment."""
        execution_id = self._create_execution_id()
        
        try:
            guild = context.get('guild')
            if not guild:
                return ToolResult(
                    success=False,
                    execution_id=execution_id,
                    tool_name=self.name,
                    data={},
                    message="No guild context provided",
                    execution_time=0.0,
                    error="Missing guild context"
                )
            
            role_id = params['role_id']
            member_ids = params['member_ids']
            
            # Get the role
            role = guild.get_role(role_id)
            if not role:
                return ToolResult(
                    success=False,
                    execution_id=execution_id,
                    tool_name=self.name,
                    data={},
                    message=f"Role with ID {role_id} not found",
                    execution_time=0.0,
                    error="Role not found"
                )
            
            # Assign role to each member
            successful_assignments = []
            failed_assignments = []
            
            for member_id in member_ids:
                try:
                    member = guild.get_member(member_id)
                    if not member:
                        failed_assignments.append({
                            'member_id': member_id,
                            'reason': 'Member not found'
                        })
                        continue
                    
                    await member.add_roles(role, reason="Assigned by AgenticMod")
                    successful_assignments.append({
                        'member_id': member_id,
                        'member_name': str(member)
                    })
                    
                except Exception as e:
                    failed_assignments.append({
                        'member_id': member_id,
                        'reason': str(e)
                    })
            
            success = len(successful_assignments) > 0
            
            logger.info(f"Assigned role {role.name} to {len(successful_assignments)} members in {guild.name}")
            
            return ToolResult(
                success=success,
                execution_id=execution_id,
                tool_name=self.name,
                data={
                    'role_id': role_id,
                    'role_name': role.name,
                    'successful_assignments': successful_assignments,
                    'failed_assignments': failed_assignments
                },
                message=f"Assigned role '{role.name}' to {len(successful_assignments)} members",
                execution_time=0.0,
                rollback_data={
                    'action': 'remove_role',
                    'role_id': role_id,
                    'member_ids': [a['member_id'] for a in successful_assignments]
                }
            )
            
        except discord.Forbidden:
            return ToolResult(
                success=False,
                execution_id=execution_id,
                tool_name=self.name,
                data={},
                message="Insufficient permissions to assign roles",
                execution_time=0.0,
                error="Forbidden"
            )
        except Exception as e:
            logger.error(f"Error assigning roles: {e}", exc_info=True)
            return ToolResult(
                success=False,
                execution_id=execution_id,
                tool_name=self.name,
                data={},
                message=f"Failed to assign roles: {str(e)}",
                execution_time=0.0,
                error=str(e)
            )
    
    async def rollback(self, execution_id: str) -> bool:
        """Rollback role assignment by removing the roles."""
        result = self.get_execution_by_id(execution_id)
        if not result or not result.success or not result.rollback_data:
            return False
        
        try:
            logger.info(f"Rollback requested for role assignment: {execution_id}")
            return True
        except Exception as e:
            logger.error(f"Error rolling back role assignment: {e}")
            return False
    
    def _get_parameter_schema(self) -> Dict[str, Any]:
        """Get parameter schema for role assignment."""
        return {
            "type": "object",
            "properties": {
                "role_id": {
                    "type": "integer",
                    "description": "ID of the role to assign"
                },
                "member_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    },
                    "description": "List of member IDs to assign the role to"
                }
            },
            "required": ["role_id", "member_ids"]
        }
