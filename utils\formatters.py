"""
Discord embed and response formatters for AgenticMod.
"""
import discord
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

class ResponseFormatter:
    """Formats AI responses into Discord embeds and messages."""
    
    @staticmethod
    def create_intent_analysis_embed(classification: Dict[str, Any], intents: List[Dict[str, Any]]) -> discord.Embed:
        """Create embed for intent analysis results."""
        embed = discord.Embed(
            title="🎯 Intent Analysis",
            color=0x0099ff,
            timestamp=datetime.utcnow()
        )
        
        # Classification info
        classification_type = classification.get('classification', 'UNKNOWN')
        confidence = classification.get('confidence_score', 0.0)
        reasoning = classification.get('reasoning', 'No reasoning provided')
        
        embed.add_field(
            name="Classification",
            value=f"**{classification_type}** (Confidence: {confidence:.1%})",
            inline=False
        )
        
        embed.add_field(
            name="Reasoning",
            value=reasoning[:1024],  # Discord field limit
            inline=False
        )
        
        # Intent details
        if intents:
            intent_text = ""
            for i, intent in enumerate(intents[:3], 1):  # Limit to 3 intents
                description = intent.get('description', 'No description')
                priority = intent.get('priority', 1)
                intent_text += f"{i}. **Priority {priority}**: {description}\n"
            
            embed.add_field(
                name="Detected Intents",
                value=intent_text or "No intents detected",
                inline=False
            )
        
        embed.set_footer(text="AgenticMod AI Analysis")
        return embed
    
    @staticmethod
    def create_execution_plan_embed(execution_plan: Dict[str, Any]) -> discord.Embed:
        """Create embed for execution plan."""
        embed = discord.Embed(
            title="📋 Execution Plan",
            color=0x00ff00,
            timestamp=datetime.utcnow()
        )
        
        metadata = execution_plan.get('metadata', {})
        actions = execution_plan.get('actions', [])
        
        # Plan overview
        total_actions = metadata.get('total_actions', len(actions))
        estimated_time = metadata.get('estimated_time', 0.0)
        risk_level = metadata.get('risk_level', 'unknown')
        
        embed.add_field(
            name="Overview",
            value=f"**Actions**: {total_actions}\n**Estimated Time**: {estimated_time:.1f}s\n**Risk Level**: {risk_level.title()}",
            inline=True
        )
        
        # Actions list
        if actions:
            actions_text = ""
            for i, action in enumerate(actions[:5], 1):  # Limit to 5 actions
                tool_name = action.get('tool_name', 'unknown')
                description = action.get('description', tool_name)
                actions_text += f"{i}. {description}\n"
            
            if len(actions) > 5:
                actions_text += f"... and {len(actions) - 5} more actions"
            
            embed.add_field(
                name="Planned Actions",
                value=actions_text,
                inline=False
            )
        
        # Risk warning
        if risk_level in ['high', 'critical']:
            embed.add_field(
                name="⚠️ Warning",
                value="This plan includes high-risk operations. Please review carefully.",
                inline=False
            )
        
        embed.set_footer(text="AgenticMod Execution Planner")
        return embed
    
    @staticmethod
    def create_execution_results_embed(execution_result: Any) -> discord.Embed:
        """Create embed for execution results."""
        if execution_result.status.value == "completed":
            color = 0x00ff00
            title = "✅ Execution Complete"
        elif execution_result.status.value == "failed":
            color = 0xff0000
            title = "❌ Execution Failed"
        elif execution_result.status.value == "rolled_back":
            color = 0xff9900
            title = "🔄 Execution Rolled Back"
        else:
            color = 0x999999
            title = "⏳ Execution Status"
        
        embed = discord.Embed(
            title=title,
            color=color,
            timestamp=datetime.utcnow()
        )
        
        # Execution summary
        completed_count = len(execution_result.completed_actions)
        failed_count = len(execution_result.failed_actions)
        total_time = execution_result.total_time
        
        embed.add_field(
            name="Summary",
            value=f"**Completed**: {completed_count}\n**Failed**: {failed_count}\n**Time**: {total_time:.2f}s",
            inline=True
        )
        
        # Completed actions
        if execution_result.completed_actions:
            completed_text = ""
            for result in execution_result.completed_actions[:3]:
                completed_text += f"✅ {result.tool_name}: {result.message}\n"
            
            if len(execution_result.completed_actions) > 3:
                completed_text += f"... and {len(execution_result.completed_actions) - 3} more"
            
            embed.add_field(
                name="Completed Actions",
                value=completed_text,
                inline=False
            )
        
        # Failed actions
        if execution_result.failed_actions:
            failed_text = ""
            for result in execution_result.failed_actions[:3]:
                failed_text += f"❌ {result.tool_name}: {result.error or 'Unknown error'}\n"
            
            if len(execution_result.failed_actions) > 3:
                failed_text += f"... and {len(execution_result.failed_actions) - 3} more"
            
            embed.add_field(
                name="Failed Actions",
                value=failed_text,
                inline=False
            )
        
        # Error message
        if execution_result.error_message:
            embed.add_field(
                name="Error Details",
                value=execution_result.error_message[:1024],
                inline=False
            )
        
        # Rollback info
        if execution_result.rollback_performed:
            embed.add_field(
                name="🔄 Rollback",
                value="Rollback was performed to undo completed actions.",
                inline=False
            )
        
        embed.set_footer(text="AgenticMod Execution Engine")
        return embed
    
    @staticmethod
    def create_server_analysis_embed(analysis_data: Dict[str, Any]) -> discord.Embed:
        """Create embed for server analysis results."""
        embed = discord.Embed(
            title="📊 Server Analysis",
            color=0x0099ff,
            timestamp=datetime.utcnow()
        )
        
        guild_info = analysis_data.get('guild_info', {})
        server_score = analysis_data.get('server_score', 0)
        recommendations = analysis_data.get('recommendations', [])
        
        # Server overview
        embed.add_field(
            name="Server Overview",
            value=f"**Name**: {guild_info.get('name', 'Unknown')}\n**Members**: {guild_info.get('member_count', 0)}\n**Score**: {server_score}/100",
            inline=True
        )
        
        # Analysis details
        analysis = analysis_data.get('analysis', {})
        
        if 'channels' in analysis:
            channels = analysis['channels']
            embed.add_field(
                name="Channels",
                value=f"**Total**: {channels.get('total_channels', 0)}\n**Text**: {channels.get('text_channels', 0)}\n**Voice**: {channels.get('voice_channels', 0)}",
                inline=True
            )
        
        if 'roles' in analysis:
            roles = analysis['roles']
            embed.add_field(
                name="Roles",
                value=f"**Total**: {roles.get('total_roles', 0)}\n**Admin**: {roles.get('roles_with_admin', 0)}\n**Colored**: {roles.get('color_roles', 0)}",
                inline=True
            )
        
        # Recommendations
        if recommendations:
            rec_text = ""
            for i, rec in enumerate(recommendations[:3], 1):
                rec_text += f"{i}. {rec}\n"
            
            if len(recommendations) > 3:
                rec_text += f"... and {len(recommendations) - 3} more recommendations"
            
            embed.add_field(
                name="💡 Recommendations",
                value=rec_text,
                inline=False
            )
        
        # Score interpretation
        if server_score >= 90:
            score_text = "🏆 Excellent organization!"
        elif server_score >= 75:
            score_text = "✅ Well organized"
        elif server_score >= 60:
            score_text = "⚠️ Room for improvement"
        else:
            score_text = "🔧 Needs reorganization"
        
        embed.add_field(
            name="Score Interpretation",
            value=score_text,
            inline=False
        )
        
        embed.set_footer(text="AgenticMod Server Analyzer")
        return embed
    
    @staticmethod
    def create_error_embed(error_message: str, details: Optional[str] = None) -> discord.Embed:
        """Create embed for error messages."""
        embed = discord.Embed(
            title="❌ Error",
            description=error_message,
            color=0xff0000,
            timestamp=datetime.utcnow()
        )
        
        if details:
            embed.add_field(
                name="Details",
                value=details[:1024],
                inline=False
            )
        
        embed.set_footer(text="AgenticMod Error Handler")
        return embed
    
    @staticmethod
    def create_progress_embed(current_step: int, total_steps: int, current_action: str) -> discord.Embed:
        """Create embed for progress updates."""
        progress_percentage = (current_step / total_steps) * 100 if total_steps > 0 else 0
        progress_bar = "█" * int(progress_percentage / 10) + "░" * (10 - int(progress_percentage / 10))
        
        embed = discord.Embed(
            title="⏳ Execution in Progress",
            color=0xffaa00,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(
            name="Progress",
            value=f"{progress_bar} {progress_percentage:.1f}%\nStep {current_step}/{total_steps}",
            inline=False
        )
        
        embed.add_field(
            name="Current Action",
            value=current_action,
            inline=False
        )
        
        embed.set_footer(text="AgenticMod Execution Engine")
        return embed
