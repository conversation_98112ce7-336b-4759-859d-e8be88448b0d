"""
Abstract base class for all AgenticMod tools.
"""
import uuid
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class ToolResult:
    """Result of tool execution."""
    success: bool
    execution_id: str
    tool_name: str
    data: Dict[str, Any]
    message: str
    execution_time: float
    error: Optional[str] = None
    rollback_data: Optional[Dict[str, Any]] = None

class BaseTool(ABC):
    """Abstract base class for all AgenticMod tools."""
    
    def __init__(self, name: str, description: str, required_permissions: List[str] = None):
        self.name = name
        self.description = description
        self.required_permissions = required_permissions or []
        self.execution_history: List[ToolResult] = []
        
        logger.debug(f"Initialized tool: {self.name}")
    
    @abstractmethod
    async def validate_input(self, params: Dict[str, Any]) -> tuple[bool, str]:
        """
        Validate input parameters for the tool.
        
        Args:
            params: Input parameters to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        pass
    
    @abstractmethod
    async def execute(self, params: Dict[str, Any], context: Dict[str, Any] = None) -> ToolResult:
        """
        Execute the tool with given parameters.
        
        Args:
            params: Tool parameters
            context: Execution context (guild, channel, user, etc.)
            
        Returns:
            ToolResult with execution details
        """
        pass
    
    @abstractmethod
    async def rollback(self, execution_id: str) -> bool:
        """
        Rollback a previous execution.
        
        Args:
            execution_id: ID of the execution to rollback
            
        Returns:
            True if rollback successful, False otherwise
        """
        pass
    
    async def can_execute(self, context: Dict[str, Any]) -> tuple[bool, str]:
        """
        Check if the tool can be executed in the given context.
        
        Args:
            context: Execution context
            
        Returns:
            Tuple of (can_execute, reason)
        """
        # Check if bot has required permissions
        guild = context.get('guild')
        if not guild:
            return False, "No guild context provided"
        
        bot_member = guild.me
        if not bot_member:
            return False, "Bot not found in guild"
        
        missing_permissions = []
        for perm in self.required_permissions:
            if not getattr(bot_member.guild_permissions, perm, False):
                missing_permissions.append(perm)
        
        if missing_permissions:
            return False, f"Missing permissions: {', '.join(missing_permissions)}"
        
        return True, "Can execute"
    
    def get_execution_history(self, limit: int = 10) -> List[ToolResult]:
        """Get recent execution history."""
        return self.execution_history[-limit:]
    
    def get_execution_by_id(self, execution_id: str) -> Optional[ToolResult]:
        """Get execution result by ID."""
        for result in self.execution_history:
            if result.execution_id == execution_id:
                return result
        return None
    
    def _create_execution_id(self) -> str:
        """Create a unique execution ID."""
        return str(uuid.uuid4())
    
    def _record_execution(self, result: ToolResult) -> None:
        """Record execution result in history."""
        self.execution_history.append(result)
        
        # Keep only last 100 executions
        if len(self.execution_history) > 100:
            self.execution_history = self.execution_history[-100:]
    
    async def safe_execute(self, params: Dict[str, Any], context: Dict[str, Any] = None) -> ToolResult:
        """
        Safely execute the tool with validation and error handling.
        
        Args:
            params: Tool parameters
            context: Execution context
            
        Returns:
            ToolResult with execution details
        """
        execution_id = self._create_execution_id()
        start_time = time.time()
        
        try:
            # Validate context
            can_execute, reason = await self.can_execute(context or {})
            if not can_execute:
                result = ToolResult(
                    success=False,
                    execution_id=execution_id,
                    tool_name=self.name,
                    data={},
                    message=f"Cannot execute: {reason}",
                    execution_time=time.time() - start_time,
                    error=reason
                )
                self._record_execution(result)
                return result
            
            # Validate input
            is_valid, error_message = await self.validate_input(params)
            if not is_valid:
                result = ToolResult(
                    success=False,
                    execution_id=execution_id,
                    tool_name=self.name,
                    data={},
                    message=f"Invalid input: {error_message}",
                    execution_time=time.time() - start_time,
                    error=error_message
                )
                self._record_execution(result)
                return result
            
            # Execute the tool
            logger.info(f"Executing tool: {self.name} with ID: {execution_id}")
            result = await self.execute(params, context)
            result.execution_id = execution_id
            result.execution_time = time.time() - start_time
            
            self._record_execution(result)
            
            if result.success:
                logger.info(f"Tool {self.name} executed successfully in {result.execution_time:.3f}s")
            else:
                logger.warning(f"Tool {self.name} failed: {result.error}")
            
            return result
            
        except Exception as e:
            logger.error(f"Unexpected error in tool {self.name}: {e}", exc_info=True)
            
            result = ToolResult(
                success=False,
                execution_id=execution_id,
                tool_name=self.name,
                data={},
                message=f"Unexpected error: {str(e)}",
                execution_time=time.time() - start_time,
                error=str(e)
            )
            self._record_execution(result)
            return result
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for function calling."""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": self._get_parameter_schema(),
            "required_permissions": self.required_permissions
        }
    
    @abstractmethod
    def _get_parameter_schema(self) -> Dict[str, Any]:
        """Get parameter schema for this tool."""
        pass
    
    def __str__(self) -> str:
        return f"Tool({self.name}): {self.description}"
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(name='{self.name}')>"
