"""
Main AI reasoning engine implementing ReAct pattern for AgenticMod.
"""
import json
import time
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from groq import Groq
import logging
from config.settings import (
    GROQ_API_KEY, GROQ_MODEL_CONFIGS, DEFAULT_MODELS,
    GROQ_RATE_LIMIT_DELAY, MAX_RETRIES, RETRY_DELAY
)

logger = logging.getLogger(__name__)

class AgentCore:
    """Main AI reasoning engine implementing ReAct pattern with Groq API."""
    
    def __init__(self):
        # Validate API key
        if not GROQ_API_KEY or GROQ_API_KEY == "your_groq_api_key_here":
            logger.error("Invalid or missing Groq API key")
            raise ValueError("Groq API key not configured properly")
        
        # Configure Groq client
        self.client = Groq(api_key=GROQ_API_KEY)
        
        # Model configurations from settings
        self.model_configs = GROQ_MODEL_CONFIGS
        self.default_models = DEFAULT_MODELS
        
        # Test API connection
        self._test_api_connection()
        
        logger.info("AgentCore initialized with Groq API")
    
    def _test_api_connection(self):
        """Test Groq API connection with a simple request."""
        try:
            logger.info("Testing Groq API connection...")
            
            test_response = self.client.chat.completions.create(
                messages=[
                    {"role": "user", "content": "Test connection. Respond with just 'OK'."}
                ],
                model=self.default_models['classification'],
                max_tokens=10,
                temperature=0
            )
            
            if test_response and test_response.choices:
                response_text = test_response.choices[0].message.content.strip()
                logger.info(f"API connection test successful: {response_text}")
            else:
                logger.warning("API connection test returned empty response")
                
        except Exception as e:
            logger.error(f"API connection test failed: {e}")
            raise
    
    async def process_request(self, user_input: str, server_context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Main ReAct loop: Observe → Think → Act → Reflect
        
        Args:
            user_input: The user's natural language command
            server_context: Current server state for context-aware processing
            
        Returns:
            Dict containing processed results, actions, and execution plan
        """
        start_time = time.time()
        
        try:
            # OBSERVE: Parse and understand the input
            observation = await self._observe(user_input, server_context or {})
            
            # THINK: Plan actions based on observation
            thought = await self._think(observation)
            
            # ACT: Execute planned actions (will be implemented with tools)
            action_result = await self._act(thought)
            
            # REFLECT: Evaluate results and adjust if needed
            reflection = await self._reflect(observation, thought, action_result)
            
            processing_time = time.time() - start_time
            
            result = {
                'observation': observation,
                'thought': thought,
                'action_result': action_result,
                'reflection': reflection,
                'processing_time': processing_time,
                'success': action_result.get('success', False)
            }
            
            logger.info(f"ReAct loop completed in {processing_time:.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"Error in ReAct loop: {e}", exc_info=True)
            return self._get_error_response(str(e))
    
    async def _observe(self, user_input: str, server_context: Dict) -> Dict[str, Any]:
        """
        OBSERVE phase: Parse and understand the user input.
        """
        logger.debug("OBSERVE: Analyzing user input and context")
        
        observation = {
            'user_input': user_input,
            'server_context': server_context,
            'timestamp': time.time(),
            'input_length': len(user_input),
            'has_server_context': bool(server_context)
        }
        
        # Basic input analysis
        observation['contains_keywords'] = self._extract_keywords(user_input)
        observation['estimated_complexity'] = self._estimate_complexity(user_input)
        
        return observation
    
    async def _think(self, observation: Dict[str, Any]) -> Dict[str, Any]:
        """
        THINK phase: Plan actions based on observation.
        """
        logger.debug("THINK: Planning actions based on observation")
        
        user_input = observation['user_input']
        complexity = observation['estimated_complexity']
        
        # Select appropriate model based on complexity
        if complexity == 'SIMPLE':
            model = self.default_models['simple_processing']
        elif complexity == 'COMPLEX':
            model = self.default_models['complex_processing']
        else:
            model = self.default_models['creative_processing']
        
        # Create planning prompt
        planning_prompt = self._create_planning_prompt(observation)
        
        try:
            # Get AI planning response
            response = await self._generate_response(planning_prompt, model)
            thought_data = json.loads(response)
            
            thought = {
                'model_used': model,
                'complexity': complexity,
                'planned_actions': thought_data.get('actions', []),
                'reasoning': thought_data.get('reasoning', ''),
                'estimated_steps': len(thought_data.get('actions', [])),
                'confidence': thought_data.get('confidence', 0.5)
            }
            
            logger.debug(f"THINK: Planned {thought['estimated_steps']} actions")
            return thought
            
        except Exception as e:
            logger.error(f"Error in THINK phase: {e}")
            return {
                'model_used': model,
                'complexity': complexity,
                'planned_actions': [],
                'reasoning': f"Planning failed: {e}",
                'estimated_steps': 0,
                'confidence': 0.0,
                'error': str(e)
            }
    
    async def _act(self, thought: Dict[str, Any]) -> Dict[str, Any]:
        """
        ACT phase: Execute planned actions.
        Note: This will be enhanced when tools are implemented.
        """
        logger.debug("ACT: Executing planned actions")
        
        planned_actions = thought.get('planned_actions', [])
        
        if not planned_actions:
            return {
                'success': False,
                'executed_actions': [],
                'results': [],
                'message': 'No actions planned'
            }
        
        # For now, simulate action execution
        # This will be replaced with actual tool execution
        executed_actions = []
        results = []
        
        for action in planned_actions:
            # Simulate action execution
            action_result = {
                'action': action,
                'status': 'simulated',
                'message': f"Would execute: {action.get('type', 'unknown')} - {action.get('description', '')}"
            }
            executed_actions.append(action)
            results.append(action_result)
        
        return {
            'success': True,
            'executed_actions': executed_actions,
            'results': results,
            'message': f'Simulated {len(executed_actions)} actions'
        }
    
    async def _reflect(self, observation: Dict, thought: Dict, action_result: Dict) -> Dict[str, Any]:
        """
        REFLECT phase: Evaluate results and adjust if needed.
        """
        logger.debug("REFLECT: Evaluating results")
        
        reflection = {
            'success': action_result.get('success', False),
            'actions_executed': len(action_result.get('executed_actions', [])),
            'overall_assessment': 'completed' if action_result.get('success') else 'failed',
            'suggestions': [],
            'next_steps': []
        }
        
        # Add suggestions based on results
        if not action_result.get('success'):
            reflection['suggestions'].append("Review the request and try again")
        else:
            reflection['suggestions'].append("Actions completed successfully")
        
        return reflection
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract relevant keywords from user input."""
        keywords = []
        text_lower = text.lower()
        
        # Discord-related keywords
        discord_keywords = ['channel', 'role', 'category', 'permission', 'server', 'member', 'voice', 'text']
        action_keywords = ['create', 'delete', 'modify', 'set', 'add', 'remove', 'analyze', 'organize']
        
        for keyword in discord_keywords + action_keywords:
            if keyword in text_lower:
                keywords.append(keyword)
        
        return keywords
    
    def _estimate_complexity(self, text: str) -> str:
        """Estimate the complexity of the user request."""
        text_lower = text.lower()
        
        # Simple indicators
        simple_indicators = ['create a', 'delete', 'add', 'remove']
        # Complex indicators
        complex_indicators = ['set up', 'organize', 'multiple', 'system', 'hierarchy']
        # Creative indicators
        creative_indicators = ['design', 'suggest', 'improve', 'analyze', 'recommend']
        
        if any(indicator in text_lower for indicator in creative_indicators):
            return 'CREATIVE'
        elif any(indicator in text_lower for indicator in complex_indicators):
            return 'COMPLEX'
        else:
            return 'SIMPLE'
    
    def _create_planning_prompt(self, observation: Dict[str, Any]) -> str:
        """Create a prompt for action planning."""
        user_input = observation['user_input']
        server_context = observation['server_context']
        
        prompt = f"""
You are an AI assistant that plans Discord server management actions. Analyze the user request and create a detailed action plan.

User Request: {user_input}

Server Context: {json.dumps(server_context, indent=2)}

Respond with a JSON object containing:
{{
  "reasoning": "Brief explanation of your understanding",
  "actions": [
    {{
      "type": "action_type",
      "description": "What this action does",
      "parameters": {{"param1": "value1"}},
      "priority": 1
    }}
  ],
  "confidence": 0.0-1.0
}}

Available action types: create_channel, create_role, modify_permissions, analyze_server, create_category, organize_channels
"""
        return prompt
    
    async def _generate_response(self, prompt: str, model: str) -> str:
        """Generate response from Groq API with error handling."""
        config = self.model_configs.get(model, self.model_configs[self.default_models['classification']])
        
        for attempt in range(MAX_RETRIES):
            try:
                await asyncio.sleep(GROQ_RATE_LIMIT_DELAY)
                
                response = self.client.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=config['max_tokens'],
                    temperature=config['temperature'],
                    top_p=0.9
                )
                
                if response and response.choices:
                    return response.choices[0].message.content.strip()
                else:
                    raise Exception("Empty response from Groq API")
                    
            except Exception as e:
                logger.warning(f"Groq API attempt {attempt + 1} failed: {e}")
                if attempt < MAX_RETRIES - 1:
                    await asyncio.sleep(RETRY_DELAY * (attempt + 1))
                else:
                    raise
        
        raise Exception("All Groq API attempts failed")
    
    def _get_error_response(self, error_message: str) -> Dict[str, Any]:
        """Get a standardized error response."""
        return {
            'observation': {'error': error_message},
            'thought': {'error': 'Planning failed due to error'},
            'action_result': {'success': False, 'error': error_message},
            'reflection': {'success': False, 'error': error_message},
            'processing_time': 0.0,
            'success': False
        }
