"""
Main entry point for AgenticMod Discord bot.
"""
import asyncio
import sys
import os
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import DISCORD_TOKEN, LOG_FILE, LOG_FORMAT, LOG_LEVEL
from discord_client.bot import AgenticModBot
from discord_client.channel_manager import ChannelManager

# Set up logging
def setup_logging():
    """Set up logging configuration."""
    # Create logs directory if it doesn't exist
    log_dir = Path(LOG_FILE).parent
    log_dir.mkdir(exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, LOG_LEVEL.upper()),
        format=LOG_FORMAT,
        handlers=[
            logging.FileHandler(LOG_FILE, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Set discord.py logging level to WARNING to reduce noise
    logging.getLogger('discord').setLevel(logging.WARNING)
    logging.getLogger('discord.http').setLevel(logging.WARNING)

async def main():
    """Main function to start the AgenticMod bot."""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("=" * 50)
    logger.info("Starting AgenticMod Discord Bot...")
    logger.info("=" * 50)
    
    # Create bot instance
    bot = AgenticModBot()
    
    try:
        # Load extensions
        logger.info("Loading bot extensions...")
        
        # Load commands
        await bot.load_extension('discord_client.commands')
        logger.info("✅ Loaded commands extension")
        
        # Load events
        await bot.load_extension('discord_client.events')
        logger.info("✅ Loaded events extension")
        
        # Initialize channel manager
        bot.channel_manager = ChannelManager(bot)
        logger.info("✅ Initialized channel manager")
        
        logger.info("All extensions loaded successfully!")
        
        # Start the bot
        logger.info("Connecting to Discord...")
        await bot.start(DISCORD_TOKEN)
        
    except KeyboardInterrupt:
        logger.info("Bot shutdown requested by user (Ctrl+C)")
    except Exception as e:
        logger.error(f"Failed to start bot: {e}", exc_info=True)
        sys.exit(1)
    finally:
        if not bot.is_closed():
            logger.info("Closing bot connection...")
            await bot.close()

if __name__ == "__main__":
    try:
        # Check if we're on Windows and set the event loop policy
        if sys.platform == 'win32':
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nBot shutdown requested by user")
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)
