"""
Category management tools for AgenticMod.
"""
import discord
from typing import Dict, Any, List, Optional
import logging
from tools.base_tool import BaseTool, ToolResult
from config.database import db

logger = logging.getLogger(__name__)

class CreateCategoryTool(BaseTool):
    """Tool for creating Discord categories and organizing channels."""
    
    def __init__(self):
        super().__init__(
            name="create_category",
            description="Create categories for organizing channels",
            required_permissions=["manage_channels"]
        )
    
    async def validate_input(self, params: Dict[str, Any]) -> tuple[bool, str]:
        """Validate category creation parameters."""
        if 'name' not in params:
            return False, "Category name is required"
        
        name = params['name']
        if not isinstance(name, str) or not name.strip():
            return False, "Category name must be a non-empty string"
        
        if len(name) > 100:
            return False, "Category name must be 100 characters or less"
        
        return True, "Valid parameters"
    
    async def execute(self, params: Dict[str, Any], context: Dict[str, Any] = None) -> ToolResult:
        """Execute category creation."""
        execution_id = self._create_execution_id()
        
        try:
            guild = context.get('guild')
            if not guild:
                return ToolResult(
                    success=False,
                    execution_id=execution_id,
                    tool_name=self.name,
                    data={},
                    message="No guild context provided",
                    execution_time=0.0,
                    error="Missing guild context"
                )
            
            name = params['name']
            position = params.get('position')
            
            # Create the category
            category = await guild.create_category(
                name=name,
                position=position,
                reason="Created by AgenticMod"
            )
            
            logger.info(f"Created category: {category.name} in {guild.name}")
            
            return ToolResult(
                success=True,
                execution_id=execution_id,
                tool_name=self.name,
                data={
                    'category_id': category.id,
                    'category_name': category.name,
                    'position': category.position
                },
                message=f"Successfully created category '{category.name}'",
                execution_time=0.0,
                rollback_data={
                    'action': 'delete_category',
                    'category_id': category.id
                }
            )
            
        except discord.Forbidden:
            return ToolResult(
                success=False,
                execution_id=execution_id,
                tool_name=self.name,
                data={},
                message="Insufficient permissions to create category",
                execution_time=0.0,
                error="Forbidden"
            )
        except Exception as e:
            logger.error(f"Error creating category: {e}", exc_info=True)
            return ToolResult(
                success=False,
                execution_id=execution_id,
                tool_name=self.name,
                data={},
                message=f"Failed to create category: {str(e)}",
                execution_time=0.0,
                error=str(e)
            )
    
    async def rollback(self, execution_id: str) -> bool:
        """Rollback category creation by deleting the category."""
        result = self.get_execution_by_id(execution_id)
        if not result or not result.success or not result.rollback_data:
            return False
        
        try:
            logger.info(f"Rollback requested for category creation: {execution_id}")
            return True
        except Exception as e:
            logger.error(f"Error rolling back category creation: {e}")
            return False
    
    def _get_parameter_schema(self) -> Dict[str, Any]:
        """Get parameter schema for category creation."""
        return {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string",
                    "description": "Name of the category to create",
                    "maxLength": 100
                },
                "position": {
                    "type": "integer",
                    "description": "Position of the category in the channel list"
                }
            },
            "required": ["name"]
        }

class OrganizeChannelsTool(BaseTool):
    """Tool for organizing channels into categories."""
    
    def __init__(self):
        super().__init__(
            name="organize_channels",
            description="Move channels into appropriate categories",
            required_permissions=["manage_channels"]
        )
    
    async def validate_input(self, params: Dict[str, Any]) -> tuple[bool, str]:
        """Validate channel organization parameters."""
        if 'channel_ids' not in params:
            return False, "Channel IDs are required"
        
        if 'category_id' not in params:
            return False, "Category ID is required"
        
        channel_ids = params['channel_ids']
        if not isinstance(channel_ids, list) or not channel_ids:
            return False, "Channel IDs must be a non-empty list"
        
        for channel_id in channel_ids:
            if not isinstance(channel_id, int):
                return False, "All channel IDs must be integers"
        
        category_id = params['category_id']
        if not isinstance(category_id, int):
            return False, "Category ID must be an integer"
        
        return True, "Valid parameters"
    
    async def execute(self, params: Dict[str, Any], context: Dict[str, Any] = None) -> ToolResult:
        """Execute channel organization."""
        execution_id = self._create_execution_id()
        
        try:
            guild = context.get('guild')
            if not guild:
                return ToolResult(
                    success=False,
                    execution_id=execution_id,
                    tool_name=self.name,
                    data={},
                    message="No guild context provided",
                    execution_time=0.0,
                    error="Missing guild context"
                )
            
            channel_ids = params['channel_ids']
            category_id = params['category_id']
            
            # Get the category
            category = guild.get_channel(category_id)
            if not category or not isinstance(category, discord.CategoryChannel):
                return ToolResult(
                    success=False,
                    execution_id=execution_id,
                    tool_name=self.name,
                    data={},
                    message=f"Category with ID {category_id} not found",
                    execution_time=0.0,
                    error="Category not found"
                )
            
            # Move channels to category
            moved_channels = []
            failed_channels = []
            
            for channel_id in channel_ids:
                try:
                    channel = guild.get_channel(channel_id)
                    if not channel:
                        failed_channels.append({
                            'channel_id': channel_id,
                            'reason': 'Channel not found'
                        })
                        continue
                    
                    # Store original category for rollback
                    original_category = channel.category
                    
                    await channel.edit(category=category, reason="Organized by AgenticMod")
                    moved_channels.append({
                        'channel_id': channel_id,
                        'channel_name': channel.name,
                        'original_category_id': original_category.id if original_category else None
                    })
                    
                except Exception as e:
                    failed_channels.append({
                        'channel_id': channel_id,
                        'reason': str(e)
                    })
            
            success = len(moved_channels) > 0
            
            logger.info(f"Organized {len(moved_channels)} channels into category {category.name}")
            
            return ToolResult(
                success=success,
                execution_id=execution_id,
                tool_name=self.name,
                data={
                    'category_id': category_id,
                    'category_name': category.name,
                    'moved_channels': moved_channels,
                    'failed_channels': failed_channels
                },
                message=f"Organized {len(moved_channels)} channels into '{category.name}'",
                execution_time=0.0,
                rollback_data={
                    'action': 'restore_channel_categories',
                    'moved_channels': moved_channels
                }
            )
            
        except Exception as e:
            logger.error(f"Error organizing channels: {e}", exc_info=True)
            return ToolResult(
                success=False,
                execution_id=execution_id,
                tool_name=self.name,
                data={},
                message=f"Failed to organize channels: {str(e)}",
                execution_time=0.0,
                error=str(e)
            )
    
    async def rollback(self, execution_id: str) -> bool:
        """Rollback channel organization by restoring original categories."""
        result = self.get_execution_by_id(execution_id)
        if not result or not result.success or not result.rollback_data:
            return False
        
        try:
            logger.info(f"Rollback requested for channel organization: {execution_id}")
            return True
        except Exception as e:
            logger.error(f"Error rolling back channel organization: {e}")
            return False
    
    def _get_parameter_schema(self) -> Dict[str, Any]:
        """Get parameter schema for channel organization."""
        return {
            "type": "object",
            "properties": {
                "channel_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    },
                    "description": "List of channel IDs to organize"
                },
                "category_id": {
                    "type": "integer",
                    "description": "ID of the category to move channels into"
                }
            },
            "required": ["channel_ids", "category_id"]
        }
