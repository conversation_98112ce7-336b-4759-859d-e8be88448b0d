"""
Main Discord bot class with initialization and core functionality.
"""
import discord
from discord.ext import commands
from typing import Dict, Optional
import asyncio
import logging
from config.settings import (
    BOT_PREFIX, REQUIRED_INTENTS, BOT_DESCRIPTION, 
    SETUP_CHANNEL_NAME, ADMIN_REQUIRED_COMMANDS
)
from config.database import db

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgenticModBot(commands.Bot):
    """Main Discord bot class for AgenticMod server management."""
    
    def __init__(self):
        # Configure bot intents
        intents = discord.Intents.default()
        for intent_name, enabled in REQUIRED_INTENTS.items():
            setattr(intents, intent_name, enabled)
        
        super().__init__(
            command_prefix=BOT_PREFIX,
            intents=intents,
            description=BOT_DESCRIPTION,
            help_command=None  # We'll implement custom help
        )
        
        # Initialize database
        self.db = db
        
        # Store setup channels for quick access
        self.setup_channels: Dict[int, int] = {}  # guild_id -> channel_id
        
        logger.info("AgenticMod bot initialized")
    
    async def setup_hook(self):
        """Called when the bot is starting up."""
        logger.info("Setting up AgenticMod bot...")

        # Initialize database
        await self.db.initialize()

        # Load setup channels from database
        self.setup_channels = await self.db.get_all_setup_channels()
        logger.info(f"Loaded {len(self.setup_channels)} setup channels from database")

        # Initialize AI service
        try:
            from ai_agent.ai_service import AIService
            self.ai_service = AIService()
            logger.info("AI service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize AI service: {e}")
            self.ai_service = None

        # Sync slash commands
        try:
            synced = await self.tree.sync()
            logger.info(f"Synced {len(synced)} slash commands")
        except Exception as e:
            logger.error(f"Failed to sync slash commands: {e}")
    
    async def on_ready(self):
        """Called when the bot has successfully connected to Discord."""
        logger.info(f"AgenticMod is ready! Logged in as {self.user} (ID: {self.user.id})")
        logger.info(f"Connected to {len(self.guilds)} servers")
        
        # Verify setup channels still exist
        await self.verify_setup_channels()
        
        # Set bot status
        await self.change_presence(
            activity=discord.Activity(
                type=discord.ActivityType.watching,
                name=f"for {BOT_PREFIX}setup commands"
            )
        )
        
        logger.info("AgenticMod is fully operational!")
    
    async def on_guild_join(self, guild):
        """Called when the bot joins a new guild."""
        logger.info(f"Joined new guild: {guild.name} (ID: {guild.id})")
        
        # Send welcome message to the first available channel
        for channel in guild.text_channels:
            if channel.permissions_for(guild.me).send_messages:
                embed = discord.Embed(
                    title="🤖 AgenticMod Has Arrived!",
                    description=(
                        f"Thank you for adding AgenticMod to **{guild.name}**!\n\n"
                        f"To get started, use `{BOT_PREFIX}setup` or `/setup` in any channel.\n"
                        f"This will create a dedicated `{SETUP_CHANNEL_NAME}` channel for server management."
                    ),
                    color=0x00ff00
                )
                embed.add_field(
                    name="🚀 Quick Start",
                    value=f"Type `{BOT_PREFIX}setup` to begin!",
                    inline=False
                )
                embed.set_footer(text="AgenticMod - Autonomous AI-powered Discord moderator")
                
                try:
                    await channel.send(embed=embed)
                    break
                except discord.Forbidden:
                    continue
    
    async def on_guild_remove(self, guild):
        """Called when the bot is removed from a guild."""
        logger.info(f"Removed from guild: {guild.name} (ID: {guild.id})")
        
        # Clean up database
        await self.db.cleanup_server(guild.id)
        
        # Remove from local cache
        self.setup_channels.pop(guild.id, None)
    
    async def on_message(self, message):
        """Handle incoming messages."""
        # Ignore bot messages
        if message.author.bot:
            return

        # Check if message is in a setup channel or is a setup command
        is_setup_channel = self.is_setup_channel(message.channel.id)
        is_setup_command = message.content.startswith(f"{BOT_PREFIX}setup")
        is_clear_command = message.content.startswith(f"{BOT_PREFIX}clear")
        is_help_command = message.content.startswith(f"{BOT_PREFIX}help")

        # Handle setup channels with AI processing
        if is_setup_channel and not (is_setup_command or is_clear_command or is_help_command):
            await self.process_natural_language_command(message)
            return

        # Process regular commands
        if is_setup_command or is_clear_command or is_help_command:
            await self.process_commands(message)
    
    async def on_command_error(self, ctx, error):
        """Handle command errors."""
        if isinstance(error, commands.CommandNotFound):
            # For setup channels, we'll handle unknown commands with AI
            if self.is_setup_channel(ctx.channel.id):
                # This will be handled by the AI system in Phase 2
                await ctx.send("🤖 I'll process that request with AI once the system is fully loaded...")
            return
        
        if isinstance(error, commands.MissingPermissions):
            await ctx.send("❌ You don't have permission to use this command.")
            return
        
        if isinstance(error, commands.BotMissingPermissions):
            await ctx.send("❌ I don't have the required permissions to execute this command.")
            return
        
        logger.error(f"Command error in {ctx.guild.name if ctx.guild else 'DM'}: {error}", exc_info=True)
        
        try:
            await ctx.send("❌ An error occurred while processing your command. Please try again.")
        except:
            pass  # Channel might be deleted or bot lacks permissions
    
    def is_setup_channel(self, channel_id: int) -> bool:
        """Check if a channel is a setup channel."""
        return channel_id in self.setup_channels.values()
    
    async def get_setup_channel(self, guild_id: int) -> Optional[discord.TextChannel]:
        """Get the setup channel for a guild."""
        channel_id = self.setup_channels.get(guild_id)
        if channel_id:
            return self.get_channel(channel_id)
        return None
    
    async def verify_setup_channels(self):
        """Verify that all setup channels still exist and are accessible."""
        to_remove = []
        
        for guild_id, channel_id in self.setup_channels.items():
            channel = self.get_channel(channel_id)
            if not channel:
                logger.warning(f"Setup channel {channel_id} no longer exists for guild {guild_id}")
                to_remove.append(guild_id)
        
        # Remove invalid channels
        for guild_id in to_remove:
            await self.db.remove_setup_channel(guild_id)
            self.setup_channels.pop(guild_id, None)
        
        if to_remove:
            logger.info(f"Removed {len(to_remove)} invalid setup channels")
    
    def check_admin_permissions(self, member: discord.Member) -> bool:
        """Check if a member has administrator permissions."""
        return member.guild_permissions.administrator
    
    async def process_natural_language_command(self, message):
        """Process natural language commands using AI service."""
        if not self.ai_service:
            await message.channel.send("❌ AI service is not available. Please try again later.")
            return

        # Check permissions
        if not self.check_admin_permissions(message.author):
            await message.channel.send("❌ You need Administrator permissions to use AI commands.")
            return

        try:
            # Show typing indicator
            async with message.channel.typing():
                # Prepare context
                context = {
                    'guild': message.guild,
                    'channel': message.channel,
                    'user': message.author,
                    'server_context': await self._get_server_context(message.guild)
                }

                # Process the command
                result = await self.ai_service.process_natural_language_command(
                    message.content,
                    context
                )

                # Send response embeds
                embeds = result.get('embeds', [])
                if embeds:
                    for embed in embeds:
                        await message.channel.send(embed=embed)
                else:
                    # Fallback text response
                    if result.get('success'):
                        await message.channel.send("✅ Command processed successfully!")
                    else:
                        error_msg = result.get('error', 'Unknown error occurred')
                        await message.channel.send(f"❌ Error: {error_msg}")

        except Exception as e:
            logger.error(f"Error processing natural language command: {e}", exc_info=True)
            await message.channel.send("❌ An unexpected error occurred while processing your command.")

    async def _get_server_context(self, guild):
        """Get current server context for AI processing."""
        try:
            # Use channel manager to get structure
            if hasattr(self, 'channel_manager'):
                return await self.channel_manager.get_channel_structure(guild)
            else:
                # Basic context
                return {
                    'guild_id': guild.id,
                    'guild_name': guild.name,
                    'member_count': guild.member_count,
                    'channel_count': len(guild.channels),
                    'role_count': len(guild.roles)
                }
        except Exception as e:
            logger.error(f"Error getting server context: {e}")
            return {}

    async def close(self):
        """Clean shutdown of the bot."""
        logger.info("Shutting down AgenticMod...")
        await super().close()
