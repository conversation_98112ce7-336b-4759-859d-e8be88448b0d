"""
Commands for AgenticMod Discord bot.
"""
import discord
from discord.ext import commands
from discord import app_commands
import logging
import asyncio
from config.settings import SETUP_CHANNEL_NAME, SETUP_CHANNEL_PERMISSIONS, ADMIN_ONLY_PERMISSIONS

logger = logging.getLogger(__name__)

class AgenticModCommands(commands.Cog):
    """Commands for the AgenticMod bot."""
    
    def __init__(self, bot):
        self.bot = bot
    
    @commands.command(name='setup')
    @commands.has_permissions(administrator=True)
    async def setup_command(self, ctx):
        """Set up AgenticMod for this server (text command)."""
        await self._handle_setup(ctx)
    
    @app_commands.command(name='setup', description='Set up AgenticMod for this server')
    @app_commands.describe(
        force_recreate='Force recreate the setup channel if it already exists'
    )
    async def setup_slash(self, interaction: discord.Interaction, force_recreate: bool = False):
        """Set up AgenticMod for this server (slash command)."""
        # Check permissions
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message(
                "❌ You need Administrator permissions to set up AgenticMod.",
                ephemeral=True
            )
            return
        
        await interaction.response.defer()
        
        # Create a context-like object for unified handling
        class SlashContext:
            def __init__(self, interaction):
                self.interaction = interaction
                self.guild = interaction.guild
                self.author = interaction.user
                self.channel = interaction.channel
                self.send = self._send
            
            async def _send(self, *args, **kwargs):
                if self.interaction.response.is_done():
                    return await self.interaction.followup.send(*args, **kwargs)
                else:
                    return await self.interaction.response.send_message(*args, **kwargs)
        
        ctx = SlashContext(interaction)
        await self._handle_setup(ctx, force_recreate)
    
    async def _handle_setup(self, ctx, force_recreate: bool = False):
        """Handle setup command logic for both text and slash commands."""
        guild = ctx.guild
        
        # Check if setup channel already exists
        existing_channel = await self.bot.get_setup_channel(guild.id)
        
        if existing_channel and not force_recreate:
            embed = discord.Embed(
                title="✅ AgenticMod Already Set Up",
                description=f"AgenticMod is already configured for **{guild.name}**!",
                color=0x00ff00
            )
            embed.add_field(
                name="📍 Setup Channel",
                value=f"{existing_channel.mention}",
                inline=False
            )
            embed.add_field(
                name="🚀 Ready to Use",
                value="You can start giving me natural language commands in the setup channel!",
                inline=False
            )
            embed.set_footer(text="Use /setup force_recreate:True to recreate the channel")
            
            await ctx.send(embed=embed)
            return
        
        # Create or recreate setup channel
        try:
            # Delete existing channel if force recreating
            if existing_channel and force_recreate:
                await existing_channel.delete(reason="AgenticMod setup channel recreation")
                logger.info(f"Deleted existing setup channel in {guild.name}")
            
            # Create new setup channel
            overwrites = {
                guild.default_role: discord.PermissionOverwrite(
                    view_channel=False,
                    send_messages=False
                ),
                guild.me: discord.PermissionOverwrite(**SETUP_CHANNEL_PERMISSIONS)
            }
            
            # Give administrators access
            for role in guild.roles:
                if role.permissions.administrator:
                    overwrites[role] = discord.PermissionOverwrite(**SETUP_CHANNEL_PERMISSIONS)
            
            setup_channel = await guild.create_text_channel(
                name=SETUP_CHANNEL_NAME,
                topic="AgenticMod AI-powered server management - Administrators only",
                overwrites=overwrites,
                reason="AgenticMod setup channel creation"
            )
            
            # Store in database and cache
            await self.bot.db.set_setup_channel(guild.id, setup_channel.id)
            self.bot.setup_channels[guild.id] = setup_channel.id
            
            logger.info(f"Created setup channel in {guild.name}: {setup_channel.name}")
            
            # Send success message
            embed = discord.Embed(
                title="🎉 AgenticMod Setup Complete!",
                description=f"Successfully set up AgenticMod for **{guild.name}**!",
                color=0x00ff00
            )
            embed.add_field(
                name="📍 Setup Channel Created",
                value=f"{setup_channel.mention}",
                inline=False
            )
            embed.add_field(
                name="🤖 How to Use",
                value=(
                    "Go to the setup channel and give me natural language commands like:\n"
                    "• `Create a gaming category with voice channels`\n"
                    "• `Set up moderation roles with different permissions`\n"
                    "• `Analyze my server and suggest improvements`"
                ),
                inline=False
            )
            embed.add_field(
                name="🔒 Security",
                value="Only administrators can access the setup channel.",
                inline=False
            )
            embed.set_footer(text="AgenticMod - Autonomous AI-powered Discord moderator")
            
            await ctx.send(embed=embed)
            
            # Send welcome message to setup channel
            welcome_embed = discord.Embed(
                title="🤖 Welcome to AgenticMod!",
                description=(
                    f"Hello, administrators of **{guild.name}**!\n\n"
                    "I'm AgenticMod, your AI-powered Discord server management assistant. "
                    "I can understand natural language commands and help you manage your server efficiently."
                ),
                color=0x0099ff
            )
            welcome_embed.add_field(
                name="🎯 What I Can Do",
                value=(
                    "• Create and manage channels and categories\n"
                    "• Set up roles and permissions\n"
                    "• Analyze your server structure\n"
                    "• Organize and optimize your server layout\n"
                    "• Handle complex multi-step server management tasks"
                ),
                inline=False
            )
            welcome_embed.add_field(
                name="💬 Example Commands",
                value=(
                    "• `Create a study group category with text and voice channels`\n"
                    "• `Set up a moderation system with helper and moderator roles`\n"
                    "• `Analyze my server and tell me how to improve it`\n"
                    "• `Organize my channels into logical categories`"
                ),
                inline=False
            )
            welcome_embed.add_field(
                name="🚀 Getting Started",
                value="Just type your request in natural language, and I'll handle the rest!",
                inline=False
            )
            welcome_embed.set_footer(text="Powered by advanced AI reasoning and Discord automation")
            
            await setup_channel.send(embed=welcome_embed)
            
        except discord.Forbidden:
            await ctx.send("❌ I don't have permission to create channels. Please ensure I have the 'Manage Channels' permission.")
        except discord.HTTPException as e:
            await ctx.send(f"❌ Failed to create setup channel: {e}")
        except Exception as e:
            logger.error(f"Error during setup in {guild.name}: {e}", exc_info=True)
            await ctx.send("❌ An unexpected error occurred during setup. Please try again.")
    
    @commands.command(name='clear')
    @commands.has_permissions(manage_messages=True)
    async def clear_command(self, ctx, amount: int = 10):
        """Clear messages from the current channel (preserved from previous version)."""
        if amount < 1 or amount > 100:
            await ctx.send("❌ Please specify a number between 1 and 100.")
            return

        try:
            # Delete the command message first
            await ctx.message.delete()

            # Delete the specified number of messages
            deleted = await ctx.channel.purge(limit=amount)

            # Send confirmation message that will auto-delete
            confirmation = await ctx.send(f"✅ Cleared {len(deleted)} messages.")

            # Delete confirmation after 3 seconds
            await asyncio.sleep(3)
            try:
                await confirmation.delete()
            except discord.NotFound:
                pass  # Message already deleted

        except discord.Forbidden:
            await ctx.send("❌ I don't have permission to delete messages in this channel.")
        except discord.HTTPException as e:
            await ctx.send(f"❌ Failed to clear messages: {e}")

    @commands.command(name='help')
    async def help_command(self, ctx):
        """Show help information."""
        embed = discord.Embed(
            title="🤖 AgenticMod Help",
            description="AI-powered Discord server management assistant",
            color=0x0099ff
        )
        embed.add_field(
            name="🚀 Setup",
            value=f"`{self.bot.command_prefix}setup` or `/setup` - Set up AgenticMod for your server",
            inline=False
        )
        embed.add_field(
            name="🧹 Moderation",
            value=f"`{self.bot.command_prefix}clear [amount]` - Clear messages (1-100, default: 10)",
            inline=False
        )
        embed.add_field(
            name="💬 AI Commands",
            value=(
                "After setup, use natural language commands in the setup channel:\n"
                "• `Create a gaming category`\n"
                "• `Set up moderation roles`\n"
                "• `Analyze my server`"
            ),
            inline=False
        )
        embed.add_field(
            name="🔗 Support",
            value="For support, contact the bot developer or check the documentation.",
            inline=False
        )
        embed.set_footer(text="AgenticMod - Autonomous AI-powered Discord moderator")

        await ctx.send(embed=embed)
    
    @clear_command.error
    async def clear_error(self, ctx, error):
        """Handle clear command errors."""
        if isinstance(error, commands.MissingPermissions):
            await ctx.send("❌ You need 'Manage Messages' permission to use this command.")
        elif isinstance(error, commands.BadArgument):
            await ctx.send("❌ Please provide a valid number between 1 and 100.")
        else:
            logger.error(f"Clear command error: {error}", exc_info=True)
            await ctx.send("❌ An error occurred while clearing messages.")

    @setup_command.error
    @setup_slash.error
    async def setup_error(self, ctx, error):
        """Handle setup command errors."""
        if isinstance(error, commands.MissingPermissions):
            await ctx.send("❌ You need Administrator permissions to set up AgenticMod.")
        else:
            logger.error(f"Setup command error: {error}", exc_info=True)
            await ctx.send("❌ An error occurred during setup. Please try again.")

async def setup(bot):
    """Setup function for the cog."""
    await bot.add_cog(AgenticModCommands(bot))
