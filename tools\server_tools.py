"""
Server analysis and management tools for AgenticMod.
"""
import discord
from typing import Dict, Any, List, Optional
import logging
from datetime import datetime, timedelta
from tools.base_tool import BaseTool, ToolResult

logger = logging.getLogger(__name__)

class AnalyzeServerTool(BaseTool):
    """Tool for analyzing Discord server structure and providing insights."""
    
    def __init__(self):
        super().__init__(
            name="analyze_server",
            description="Analyze server structure, activity, and provide optimization suggestions",
            required_permissions=["view_audit_log"]  # Optional for deeper analysis
        )
    
    async def validate_input(self, params: Dict[str, Any]) -> tuple[bool, str]:
        """Validate server analysis parameters."""
        # Analysis type validation
        analysis_type = params.get('analysis_type', 'full')
        valid_types = ['full', 'channels', 'roles', 'members', 'activity']
        
        if analysis_type not in valid_types:
            return False, f"Analysis type must be one of: {', '.join(valid_types)}"
        
        return True, "Valid parameters"
    
    async def execute(self, params: Dict[str, Any], context: Dict[str, Any] = None) -> ToolResult:
        """Execute server analysis."""
        execution_id = self._create_execution_id()
        
        try:
            guild = context.get('guild')
            if not guild:
                return ToolResult(
                    success=False,
                    execution_id=execution_id,
                    tool_name=self.name,
                    data={},
                    message="No guild context provided",
                    execution_time=0.0,
                    error="Missing guild context"
                )
            
            analysis_type = params.get('analysis_type', 'full')
            
            # Perform analysis based on type
            analysis_data = {}
            
            if analysis_type in ['full', 'channels']:
                analysis_data['channels'] = await self._analyze_channels(guild)
            
            if analysis_type in ['full', 'roles']:
                analysis_data['roles'] = await self._analyze_roles(guild)
            
            if analysis_type in ['full', 'members']:
                analysis_data['members'] = await self._analyze_members(guild)
            
            if analysis_type in ['full', 'activity']:
                analysis_data['activity'] = await self._analyze_activity(guild)
            
            # Generate recommendations
            recommendations = await self._generate_recommendations(guild, analysis_data)
            
            # Calculate server score
            server_score = self._calculate_server_score(analysis_data)
            
            logger.info(f"Analyzed server: {guild.name} (Score: {server_score}/100)")
            
            return ToolResult(
                success=True,
                execution_id=execution_id,
                tool_name=self.name,
                data={
                    'guild_info': {
                        'name': guild.name,
                        'id': guild.id,
                        'member_count': guild.member_count,
                        'created_at': guild.created_at.isoformat(),
                        'owner_id': guild.owner_id
                    },
                    'analysis': analysis_data,
                    'recommendations': recommendations,
                    'server_score': server_score,
                    'analysis_type': analysis_type
                },
                message=f"Server analysis complete. Score: {server_score}/100",
                execution_time=0.0
            )
            
        except Exception as e:
            logger.error(f"Error analyzing server: {e}", exc_info=True)
            return ToolResult(
                success=False,
                execution_id=execution_id,
                tool_name=self.name,
                data={},
                message=f"Failed to analyze server: {str(e)}",
                execution_time=0.0,
                error=str(e)
            )
    
    async def _analyze_channels(self, guild: discord.Guild) -> Dict[str, Any]:
        """Analyze channel structure."""
        channels_data = {
            'total_channels': len(guild.channels),
            'text_channels': len(guild.text_channels),
            'voice_channels': len(guild.voice_channels),
            'categories': len(guild.categories),
            'channels_without_category': 0,
            'empty_categories': 0,
            'channel_distribution': {}
        }
        
        # Count channels without categories
        for channel in guild.channels:
            if not channel.category and not isinstance(channel, discord.CategoryChannel):
                channels_data['channels_without_category'] += 1
        
        # Analyze categories
        for category in guild.categories:
            channel_count = len(category.channels)
            if channel_count == 0:
                channels_data['empty_categories'] += 1
            
            channels_data['channel_distribution'][category.name] = {
                'text_channels': len(category.text_channels),
                'voice_channels': len(category.voice_channels),
                'total': channel_count
            }
        
        return channels_data
    
    async def _analyze_roles(self, guild: discord.Guild) -> Dict[str, Any]:
        """Analyze role structure."""
        roles_data = {
            'total_roles': len(guild.roles) - 1,  # Exclude @everyone
            'hoisted_roles': 0,
            'mentionable_roles': 0,
            'roles_with_admin': 0,
            'roles_with_dangerous_perms': 0,
            'color_roles': 0
        }
        
        dangerous_perms = [
            'administrator', 'manage_guild', 'manage_roles', 
            'manage_channels', 'kick_members', 'ban_members'
        ]
        
        for role in guild.roles:
            if role.name == '@everyone':
                continue
                
            if role.hoist:
                roles_data['hoisted_roles'] += 1
            
            if role.mentionable:
                roles_data['mentionable_roles'] += 1
            
            if role.color.value != 0:
                roles_data['color_roles'] += 1
            
            if role.permissions.administrator:
                roles_data['roles_with_admin'] += 1
            
            for perm in dangerous_perms:
                if getattr(role.permissions, perm, False):
                    roles_data['roles_with_dangerous_perms'] += 1
                    break
        
        return roles_data
    
    async def _analyze_members(self, guild: discord.Guild) -> Dict[str, Any]:
        """Analyze member statistics."""
        members_data = {
            'total_members': guild.member_count,
            'bot_count': 0,
            'human_count': 0,
            'members_with_roles': 0,
            'members_without_roles': 0,
            'online_members': 0
        }
        
        # Note: This requires member intent for accurate data
        try:
            for member in guild.members:
                if member.bot:
                    members_data['bot_count'] += 1
                else:
                    members_data['human_count'] += 1
                
                if len(member.roles) > 1:  # More than @everyone
                    members_data['members_with_roles'] += 1
                else:
                    members_data['members_without_roles'] += 1
                
                if member.status != discord.Status.offline:
                    members_data['online_members'] += 1
        except:
            # If member intent is not available, use approximations
            members_data['human_count'] = guild.member_count
            members_data['bot_count'] = 0
        
        return members_data
    
    async def _analyze_activity(self, guild: discord.Guild) -> Dict[str, Any]:
        """Analyze server activity patterns."""
        activity_data = {
            'boost_level': guild.premium_tier,
            'boost_count': guild.premium_subscription_count or 0,
            'verification_level': str(guild.verification_level),
            'explicit_content_filter': str(guild.explicit_content_filter),
            'has_welcome_screen': guild.welcome_screen is not None,
            'has_rules_channel': guild.rules_channel is not None,
            'has_system_channel': guild.system_channel is not None
        }
        
        return activity_data
    
    async def _generate_recommendations(self, guild: discord.Guild, analysis_data: Dict[str, Any]) -> List[str]:
        """Generate optimization recommendations based on analysis."""
        recommendations = []
        
        # Channel recommendations
        if 'channels' in analysis_data:
            channels = analysis_data['channels']
            
            if channels['channels_without_category'] > 3:
                recommendations.append(
                    f"Consider organizing {channels['channels_without_category']} uncategorized channels into categories"
                )
            
            if channels['empty_categories'] > 0:
                recommendations.append(
                    f"Remove {channels['empty_categories']} empty categories to reduce clutter"
                )
            
            if channels['total_channels'] > 50:
                recommendations.append(
                    "Consider archiving unused channels - you have many channels which may overwhelm users"
                )
        
        # Role recommendations
        if 'roles' in analysis_data:
            roles = analysis_data['roles']
            
            if roles['roles_with_admin'] > 3:
                recommendations.append(
                    "Consider reducing the number of roles with administrator permissions for better security"
                )
            
            if roles['total_roles'] > 20:
                recommendations.append(
                    "Consider consolidating roles - too many roles can be confusing for members"
                )
        
        # Member recommendations
        if 'members' in analysis_data:
            members = analysis_data['members']
            
            if members['members_without_roles'] > members['total_members'] * 0.5:
                recommendations.append(
                    "Consider implementing an auto-role system - many members don't have roles assigned"
                )
        
        # Activity recommendations
        if 'activity' in analysis_data:
            activity = analysis_data['activity']
            
            if not activity['has_welcome_screen']:
                recommendations.append(
                    "Set up a welcome screen to help new members understand your server"
                )
            
            if not activity['has_rules_channel']:
                recommendations.append(
                    "Create a rules channel to establish clear community guidelines"
                )
        
        return recommendations
    
    def _calculate_server_score(self, analysis_data: Dict[str, Any]) -> int:
        """Calculate overall server organization score (0-100)."""
        score = 100
        
        # Channel organization penalties
        if 'channels' in analysis_data:
            channels = analysis_data['channels']
            
            # Penalty for uncategorized channels
            if channels['channels_without_category'] > 5:
                score -= min(20, channels['channels_without_category'] * 2)
            
            # Penalty for empty categories
            score -= channels['empty_categories'] * 5
            
            # Penalty for too many channels
            if channels['total_channels'] > 30:
                score -= min(15, (channels['total_channels'] - 30) * 0.5)
        
        # Role organization penalties
        if 'roles' in analysis_data:
            roles = analysis_data['roles']
            
            # Penalty for too many admin roles
            if roles['roles_with_admin'] > 3:
                score -= (roles['roles_with_admin'] - 3) * 5
            
            # Penalty for too many roles
            if roles['total_roles'] > 15:
                score -= min(10, (roles['total_roles'] - 15) * 0.5)
        
        # Activity bonuses
        if 'activity' in analysis_data:
            activity = analysis_data['activity']
            
            if activity['has_welcome_screen']:
                score += 5
            
            if activity['has_rules_channel']:
                score += 5
            
            if activity['boost_level'] > 0:
                score += activity['boost_level'] * 3
        
        return max(0, min(100, int(score)))
    
    async def rollback(self, execution_id: str) -> bool:
        """Analysis doesn't require rollback."""
        return True
    
    def _get_parameter_schema(self) -> Dict[str, Any]:
        """Get parameter schema for server analysis."""
        return {
            "type": "object",
            "properties": {
                "analysis_type": {
                    "type": "string",
                    "enum": ["full", "channels", "roles", "members", "activity"],
                    "description": "Type of analysis to perform",
                    "default": "full"
                }
            }
        }
