"""
Main AI service integrating all AgenticMod AI components.
"""
import asyncio
import time
from typing import Dict, List, Any, Optional
import logging

from ai_agent.agent_core import AgentCore
from ai_agent.intent_parser import IntentParser
from ai_agent.action_planner import ActionPlanner
from ai_agent.tool_executor import ToolExecutor
from utils.formatters import ResponseFormatter

logger = logging.getLogger(__name__)

class AIService:
    """Main AI service coordinating all AgenticMod AI components."""
    
    def __init__(self):
        # Initialize AI components
        self.agent_core = AgentCore()
        self.intent_parser = IntentParser()
        self.action_planner = ActionPlanner()
        self.tool_executor = ToolExecutor()
        self.formatter = ResponseFormatter()
        
        logger.info("AIService initialized with all components")
    
    async def process_natural_language_command(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a natural language command through the complete AI pipeline.
        
        Args:
            user_input: User's natural language command
            context: Execution context (guild, user, channel, etc.)
            
        Returns:
            Complete processing result with embeds and execution details
        """
        start_time = time.time()
        
        try:
            logger.info(f"Processing command: {user_input[:100]}...")
            
            # STAGE 1-3: Parse intent using three-stage pipeline
            intent_result = await self.intent_parser.parse_intent(user_input, context.get('server_context'))
            
            # Check if parsing was successful
            if not intent_result or intent_result.get('stage') == 'FALLBACK':
                return {
                    'success': False,
                    'error': 'Failed to parse command',
                    'embeds': [self.formatter.create_error_embed(
                        "I couldn't understand your request. Please try rephrasing it.",
                        "The AI parsing system was unable to interpret your command."
                    )],
                    'processing_time': time.time() - start_time
                }
            
            # Extract intents from result
            intents = intent_result.get('intents', [])
            if not intents:
                # For simple requests, create a basic intent structure
                actions = intent_result.get('actions', [])
                if actions:
                    intents = [{
                        'intent_id': 'simple_action',
                        'description': intent_result.get('summary', 'Execute action'),
                        'priority': 1,
                        'actions': actions
                    }]
            
            if not intents:
                return {
                    'success': False,
                    'error': 'No actionable intents found',
                    'embeds': [self.formatter.create_error_embed(
                        "I couldn't find any actions to perform in your request.",
                        "Please be more specific about what you'd like me to do."
                    )],
                    'processing_time': time.time() - start_time
                }
            
            # Create intent analysis embed
            classification = intent_result.get('classification', {})
            intent_embed = self.formatter.create_intent_analysis_embed(classification, intents)
            
            # STAGE 4: Create execution plan
            execution_plan = await self.action_planner.create_execution_plan(intents, context)
            
            if not execution_plan.get('dependencies_resolved', False):
                return {
                    'success': False,
                    'error': 'Failed to create execution plan',
                    'embeds': [
                        intent_embed,
                        self.formatter.create_error_embed(
                            "I couldn't create a valid execution plan for your request.",
                            execution_plan.get('metadata', {}).get('error', 'Unknown planning error')
                        )
                    ],
                    'processing_time': time.time() - start_time
                }
            
            # Create execution plan embed
            plan_embed = self.formatter.create_execution_plan_embed(execution_plan)
            
            # STAGE 5: Validate execution plan
            is_valid, issues = await self.action_planner.validate_plan(execution_plan, context)
            
            if not is_valid:
                return {
                    'success': False,
                    'error': 'Invalid execution plan',
                    'embeds': [
                        intent_embed,
                        plan_embed,
                        self.formatter.create_error_embed(
                            "The execution plan has validation issues:",
                            "\n".join(issues)
                        )
                    ],
                    'processing_time': time.time() - start_time
                }
            
            # STAGE 6: Execute the plan
            execution_result = await self.tool_executor.execute_plan(execution_plan, context)
            
            # Create execution results embed
            results_embed = self.formatter.create_execution_results_embed(execution_result)
            
            # Determine overall success
            overall_success = execution_result.status.value in ['completed']
            
            processing_time = time.time() - start_time
            
            result = {
                'success': overall_success,
                'intent_result': intent_result,
                'execution_plan': execution_plan,
                'execution_result': execution_result,
                'embeds': [intent_embed, plan_embed, results_embed],
                'processing_time': processing_time
            }
            
            logger.info(f"Command processing completed in {processing_time:.3f}s (Success: {overall_success})")
            return result
            
        except Exception as e:
            logger.error(f"Error in AI service processing: {e}", exc_info=True)
            
            return {
                'success': False,
                'error': str(e),
                'embeds': [self.formatter.create_error_embed(
                    "An unexpected error occurred while processing your command.",
                    f"Error: {str(e)}"
                )],
                'processing_time': time.time() - start_time
            }
    
    async def analyze_server(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform server analysis using the AnalyzeServerTool.
        
        Args:
            context: Execution context with guild information
            
        Returns:
            Analysis result with formatted embed
        """
        try:
            # Create a simple execution plan for server analysis
            analysis_plan = {
                'actions': [{
                    'action_id': 'analyze_server_1',
                    'tool_name': 'analyze_server',
                    'parameters': {'analysis_type': 'full'},
                    'dependencies': [],
                    'priority': 1
                }],
                'metadata': {
                    'total_actions': 1,
                    'estimated_time': 3.0,
                    'risk_level': 'minimal'
                }
            }
            
            # Execute the analysis
            execution_result = await self.tool_executor.execute_plan(analysis_plan, context)
            
            if execution_result.completed_actions:
                analysis_data = execution_result.completed_actions[0].data
                analysis_embed = self.formatter.create_server_analysis_embed(analysis_data)
                
                return {
                    'success': True,
                    'analysis_data': analysis_data,
                    'embeds': [analysis_embed]
                }
            else:
                return {
                    'success': False,
                    'error': 'Analysis failed',
                    'embeds': [self.formatter.create_error_embed(
                        "Server analysis failed",
                        "Unable to analyze the server structure."
                    )]
                }
                
        except Exception as e:
            logger.error(f"Error in server analysis: {e}", exc_info=True)
            return {
                'success': False,
                'error': str(e),
                'embeds': [self.formatter.create_error_embed(
                    "Server analysis error",
                    f"Error: {str(e)}"
                )]
            }
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get status of all AI system components."""
        try:
            tool_status = await self.tool_executor.get_tool_status()
            
            return {
                'agent_core': 'operational',
                'intent_parser': 'operational',
                'action_planner': 'operational',
                'tool_executor': tool_status,
                'available_tools': tool_status.get('available_tools', []),
                'system_health': 'healthy'
            }
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {
                'system_health': 'error',
                'error': str(e)
            }
    
    def get_capabilities(self) -> List[str]:
        """Get list of AI system capabilities."""
        return [
            "Natural language command processing",
            "Three-stage intent classification (SIMPLE/COMPLEX/CREATIVE)",
            "Multi-step action planning with dependency resolution",
            "Channel creation and management",
            "Role creation and assignment",
            "Server structure analysis and optimization",
            "Automatic rollback on critical failures",
            "Real-time execution progress tracking"
        ]
