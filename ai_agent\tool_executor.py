"""
Tool execution engine with error handling and rollback capabilities for AgenticMod.
"""
import asyncio
import time
from typing import Dict, List, Any, Optional, Tuple
import logging
from dataclasses import dataclass
from enum import Enum

from tools.base_tool import BaseTool, ToolResult
from tools.channel_tools import Create<PERSON>han<PERSON>Tool, DeleteChannelTool
from tools.role_tools import CreateRoleTool, AssignRoleTool
from tools.server_tools import AnalyzeServerTool
from tools.category_tools import CreateCategoryTool, OrganizeChannelsTool

logger = logging.getLogger(__name__)

class ExecutionStatus(Enum):
    """Status of execution."""
    NOT_STARTED = "not_started"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    ROLLED_BACK = "rolled_back"

@dataclass
class ExecutionResult:
    """Result of tool execution sequence."""
    status: ExecutionStatus
    completed_actions: List[ToolResult]
    failed_actions: List[ToolResult]
    total_time: float
    rollback_performed: bool = False
    error_message: Optional[str] = None

class ToolExecutor:
    """Executes planned actions with error handling and rollback capabilities."""
    
    def __init__(self):
        # Initialize available tools
        self.tools: Dict[str, BaseTool] = {
            'create_channel': CreateChannelTool(),
            'delete_channel': DeleteChannelTool(),
            'create_role': CreateRoleTool(),
            'assign_role': AssignRoleTool(),
            'analyze_server': AnalyzeServerTool(),
            'create_category': CreateCategoryTool(),
            'organize_channels': OrganizeChannelsTool()
        }
        
        # Execution tracking
        self.execution_history: List[ExecutionResult] = []
        self.current_execution: Optional[str] = None
        
        logger.info(f"ToolExecutor initialized with {len(self.tools)} tools")
    
    async def execute_plan(self, execution_plan: Dict[str, Any], context: Dict[str, Any]) -> ExecutionResult:
        """
        Execute a planned sequence of actions.
        
        Args:
            execution_plan: The execution plan from ActionPlanner
            context: Execution context (guild, user, etc.)
            
        Returns:
            ExecutionResult with details of what was executed
        """
        start_time = time.time()
        execution_id = f"exec_{int(start_time)}"
        self.current_execution = execution_id
        
        completed_actions = []
        failed_actions = []
        
        try:
            actions = execution_plan.get('actions', [])
            
            if not actions:
                return ExecutionResult(
                    status=ExecutionStatus.FAILED,
                    completed_actions=[],
                    failed_actions=[],
                    total_time=0.0,
                    error_message="No actions to execute"
                )
            
            logger.info(f"Starting execution of {len(actions)} actions (ID: {execution_id})")
            
            # Execute actions in order
            for i, action_data in enumerate(actions):
                action_id = action_data.get('action_id', f'action_{i}')
                tool_name = action_data.get('tool_name')
                parameters = action_data.get('parameters', {})
                
                logger.info(f"Executing action {i+1}/{len(actions)}: {tool_name} ({action_id})")
                
                # Get the tool
                tool = self.tools.get(tool_name)
                if not tool:
                    error_result = ToolResult(
                        success=False,
                        execution_id=action_id,
                        tool_name=tool_name,
                        data={},
                        message=f"Tool '{tool_name}' not found",
                        execution_time=0.0,
                        error=f"Unknown tool: {tool_name}"
                    )
                    failed_actions.append(error_result)
                    
                    # Decide whether to continue or stop
                    if self._should_stop_on_error(action_data, execution_plan):
                        logger.error(f"Stopping execution due to critical error in {tool_name}")
                        break
                    else:
                        logger.warning(f"Continuing execution despite error in {tool_name}")
                        continue
                
                # Execute the tool
                try:
                    result = await tool.safe_execute(parameters, context)
                    
                    if result.success:
                        completed_actions.append(result)
                        logger.info(f"✅ Action {action_id} completed successfully")
                        
                        # Update context with results for dependent actions
                        self._update_context_with_result(context, result)
                        
                    else:
                        failed_actions.append(result)
                        logger.error(f"❌ Action {action_id} failed: {result.error}")
                        
                        # Decide whether to continue or rollback
                        if self._should_rollback_on_error(action_data, execution_plan):
                            logger.info("Initiating rollback due to critical failure")
                            rollback_success = await self._rollback_actions(completed_actions)
                            
                            return ExecutionResult(
                                status=ExecutionStatus.ROLLED_BACK,
                                completed_actions=completed_actions,
                                failed_actions=failed_actions,
                                total_time=time.time() - start_time,
                                rollback_performed=rollback_success,
                                error_message=f"Execution failed at {tool_name}, rollback performed"
                            )
                        
                except Exception as e:
                    logger.error(f"Unexpected error executing {tool_name}: {e}", exc_info=True)
                    
                    error_result = ToolResult(
                        success=False,
                        execution_id=action_id,
                        tool_name=tool_name,
                        data={},
                        message=f"Unexpected error: {str(e)}",
                        execution_time=0.0,
                        error=str(e)
                    )
                    failed_actions.append(error_result)
                    
                    if self._should_stop_on_error(action_data, execution_plan):
                        break
            
            # Determine final status
            total_time = time.time() - start_time
            
            if failed_actions and not completed_actions:
                status = ExecutionStatus.FAILED
            elif failed_actions:
                status = ExecutionStatus.COMPLETED  # Partial success
            else:
                status = ExecutionStatus.COMPLETED
            
            result = ExecutionResult(
                status=status,
                completed_actions=completed_actions,
                failed_actions=failed_actions,
                total_time=total_time
            )
            
            self.execution_history.append(result)
            logger.info(f"Execution completed: {len(completed_actions)} successful, {len(failed_actions)} failed")
            
            return result
            
        except Exception as e:
            logger.error(f"Critical error in execution: {e}", exc_info=True)
            
            return ExecutionResult(
                status=ExecutionStatus.FAILED,
                completed_actions=completed_actions,
                failed_actions=failed_actions,
                total_time=time.time() - start_time,
                error_message=f"Critical execution error: {str(e)}"
            )
        
        finally:
            self.current_execution = None
    
    def _update_context_with_result(self, context: Dict[str, Any], result: ToolResult) -> None:
        """Update execution context with results for dependent actions."""
        if not result.success:
            return
        
        # Store results that other actions might need
        if 'execution_results' not in context:
            context['execution_results'] = {}
        
        context['execution_results'][result.execution_id] = result.data
        
        # Store specific data types for easy access
        if result.tool_name == 'create_channel' and 'channel_id' in result.data:
            if 'created_channels' not in context:
                context['created_channels'] = []
            context['created_channels'].append(result.data['channel_id'])
        
        elif result.tool_name == 'create_role' and 'role_id' in result.data:
            if 'created_roles' not in context:
                context['created_roles'] = []
            context['created_roles'].append(result.data['role_id'])
    
    def _should_stop_on_error(self, action_data: Dict[str, Any], execution_plan: Dict[str, Any]) -> bool:
        """Determine if execution should stop on error."""
        # Stop on critical tools
        critical_tools = ['delete_channel', 'delete_role', 'kick_member', 'ban_member']
        if action_data.get('tool_name') in critical_tools:
            return True
        
        # Stop if marked as critical in the plan
        if action_data.get('critical', False):
            return True
        
        # Stop if risk level is high
        risk_level = execution_plan.get('metadata', {}).get('risk_level', 'low')
        if risk_level in ['high', 'critical']:
            return True
        
        return False
    
    def _should_rollback_on_error(self, action_data: Dict[str, Any], execution_plan: Dict[str, Any]) -> bool:
        """Determine if rollback should be performed on error."""
        # Always rollback for destructive operations
        destructive_tools = ['delete_channel', 'delete_role']
        if action_data.get('tool_name') in destructive_tools:
            return True
        
        # Rollback if explicitly requested
        if action_data.get('rollback_on_error', False):
            return True
        
        # Rollback for high-risk operations
        risk_level = execution_plan.get('metadata', {}).get('risk_level', 'low')
        if risk_level == 'high':
            return True
        
        return False
    
    async def _rollback_actions(self, completed_actions: List[ToolResult]) -> bool:
        """Rollback completed actions in reverse order."""
        if not completed_actions:
            return True
        
        logger.info(f"Rolling back {len(completed_actions)} completed actions")
        rollback_success = True
        
        # Rollback in reverse order
        for result in reversed(completed_actions):
            try:
                tool = self.tools.get(result.tool_name)
                if tool and hasattr(tool, 'rollback'):
                    success = await tool.rollback(result.execution_id)
                    if not success:
                        logger.error(f"Failed to rollback {result.tool_name} (ID: {result.execution_id})")
                        rollback_success = False
                    else:
                        logger.info(f"✅ Rolled back {result.tool_name} (ID: {result.execution_id})")
                else:
                    logger.warning(f"No rollback available for {result.tool_name}")
                    
            except Exception as e:
                logger.error(f"Error during rollback of {result.tool_name}: {e}")
                rollback_success = False
        
        return rollback_success
    
    async def get_tool_status(self) -> Dict[str, Any]:
        """Get status of all available tools."""
        status = {
            'available_tools': list(self.tools.keys()),
            'tool_count': len(self.tools),
            'execution_history_count': len(self.execution_history),
            'current_execution': self.current_execution,
            'tools_detail': {}
        }
        
        for name, tool in self.tools.items():
            status['tools_detail'][name] = {
                'description': tool.description,
                'required_permissions': tool.required_permissions,
                'execution_count': len(tool.execution_history)
            }
        
        return status
    
    def get_execution_history(self, limit: int = 10) -> List[ExecutionResult]:
        """Get recent execution history."""
        return self.execution_history[-limit:]
    
    def add_tool(self, name: str, tool: BaseTool) -> None:
        """Add a new tool to the executor."""
        self.tools[name] = tool
        logger.info(f"Added tool: {name}")
    
    def remove_tool(self, name: str) -> bool:
        """Remove a tool from the executor."""
        if name in self.tools:
            del self.tools[name]
            logger.info(f"Removed tool: {name}")
            return True
        return False
