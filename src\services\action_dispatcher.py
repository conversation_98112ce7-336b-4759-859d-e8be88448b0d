"""
Action dispatcher for executing structured bot actions.
Enhanced with multi-step execution capabilities.
"""
import discord
from typing import Dict, List, Any, Optional, Tuple
from src.utils.logger import bot_logger
from src.services.execution_engine import MultiStepExecutionEngine, ExecutionPlan

class ActionDispatcher:
    """Executes structured actions on Discord servers."""

    def __init__(self, bot):
        self.bot = bot
        # Import services here to avoid circular imports
        from src.services.channel_manager import ChannelManager
        from src.services.role_manager import RoleManager
        from src.services.server_templates import ServerTemplateService

        self.channel_manager = ChannelManager(bot)
        self.role_manager = RoleManager(bot)
        self.template_service = ServerTemplateService(bot)
        self.execution_engine = MultiStepExecutionEngine(self)
    
    async def execute_actions(self, actions: List[Dict[str, Any]], guild: discord.Guild) -> Tuple[List[str], List[str]]:
        """
        Execute a list of actions on a guild.
        
        Args:
            actions: List of action dictionaries
            guild: Discord guild to execute actions on
            
        Returns:
            Tuple of (success_messages, error_messages)
        """
        success_messages = []
        error_messages = []
        
        for action in actions:
            try:
                result = await self._execute_single_action(action, guild)
                if result:
                    success_messages.append(result)
            except Exception as e:
                error_msg = f"Failed to execute {action.get('type', 'unknown')}: {str(e)}"
                error_messages.append(error_msg)
                bot_logger.error(f"Action execution error: {e}", exc_info=True)
        
        return success_messages, error_messages

    async def execute_enhanced_actions(
        self,
        parsed_response: Dict[str, Any],
        guild: discord.Guild,
        progress_callback: Optional[callable] = None
    ) -> Tuple[List[str], List[str], Dict[str, Any]]:
        """
        Execute enhanced multi-step actions with dependency resolution and rollback capability.

        Args:
            parsed_response: Enhanced response from AI service with intents, actions, execution_plan
            guild: Discord guild to execute actions on
            progress_callback: Optional callback for progress updates

        Returns:
            Tuple of (success_messages, error_messages, execution_summary)
        """
        actions = parsed_response.get('actions', [])
        execution_plan_data = parsed_response.get('execution_plan', {})
        dependencies = parsed_response.get('dependencies', {})

        if not actions:
            return [], ["No actions to execute"], {}

        # Create execution plan
        execution_plan = ExecutionPlan(
            total_steps=execution_plan_data.get('total_steps', len(actions)),
            estimated_time=execution_plan_data.get('estimated_time', f"{len(actions) * 15} seconds"),
            rollback_strategy=execution_plan_data.get('rollback_strategy', 'delete_created_items'),
            risk_level=execution_plan_data.get('risk_level', 'low'),
            dependencies=dependencies
        )

        bot_logger.info(f"Executing enhanced multi-step operation: {len(actions)} actions, risk level: {execution_plan.risk_level}")

        # Execute using enhanced execution engine
        return await self.execution_engine.execute_multi_step_operation(
            actions, execution_plan, guild, progress_callback
        )
    
    async def _execute_single_action(self, action: Dict[str, Any], guild: discord.Guild) -> Optional[str]:
        """Execute a single action and return success message."""
        action_type = action.get('type')

        # Basic actions
        if action_type == 'create_channel':
            return await self._create_channel(action, guild)
        elif action_type == 'create_category':
            return await self._create_category(action, guild)
        elif action_type == 'delete_channel':
            return await self._delete_channel(action, guild)
        elif action_type == 'delete_category':
            return await self._delete_category(action, guild)
        elif action_type == 'create_role':
            return await self._create_role(action, guild)
        elif action_type == 'delete_role':
            return await self._delete_role(action, guild)
        elif action_type == 'modify_permissions':
            return await self._modify_permissions(action, guild)
        elif action_type == 'analyze_server':
            return await self._analyze_server(action, guild)
        elif action_type == 'edit_channel_permissions':
            return await self._edit_channel_permissions(action, guild)
        elif action_type == 'edit_channel':
            return await self._edit_channel(action, guild)

        # Advanced channel management actions
        # Note: create_channel_template is handled in the main action routing section below
        elif action_type == 'organize_channels':
            results = await self.channel_manager.organize_channels_by_type(guild)
            return "\n".join(results)
        elif action_type == 'duplicate_channel':
            source = action.get('source_channel')
            target = action.get('target_channel')
            return await self.channel_manager.duplicate_channel(guild, source, target)
        elif action_type == 'bulk_delete_channels':
            pattern = action.get('pattern')
            results = await self.channel_manager.bulk_delete_channels(guild, pattern)
            return "\n".join(results)

        # Advanced role management actions
        elif action_type == 'create_role_hierarchy':
            hierarchy_type = action.get('hierarchy_type')
            results = await self.role_manager.create_role_hierarchy(guild, hierarchy_type)
            return "\n".join(results)
        elif action_type == 'assign_role_colors':
            color_scheme = action.get('color_scheme')
            results = await self.role_manager.assign_role_colors(guild, color_scheme)
            return "\n".join(results)
        elif action_type == 'cleanup_roles':
            results = await self.role_manager.cleanup_unused_roles(guild)
            return "\n".join(results)
        elif action_type == 'organize_role_hierarchy':
            results = await self.role_manager.organize_role_hierarchy(guild)
            return "\n".join(results)
        elif action_type == 'bulk_assign_role':
            role_name = action.get('role_name')
            criteria = action.get('criteria')
            results = await self.role_manager.bulk_assign_role(guild, role_name, criteria)
            return "\n".join(results)

        # Advanced template actions
        elif action_type == 'apply_server_template':
            template_name = action.get('template_name')
            results = await self.template_service.apply_complete_template(guild, template_name)
            return "\n".join(results)

        # Structural reorganization actions
        elif action_type == 'reorganize_server_structure':
            return await self._reorganize_server_structure(action, guild)
        elif action_type == 'merge_channels':
            return await self._merge_channels(action, guild)
        elif action_type == 'auto_categorize':
            return await self._auto_categorize(action, guild)
        elif action_type == 'suggest_organization':
            return await self._suggest_organization(action, guild)
        elif action_type == 'analyze_redundancy':
            return await self._analyze_redundancy(action, guild)
        elif action_type == 'optimize_structure':
            return await self._optimize_structure(action, guild)

        # Emoji and reaction actions
        elif action_type == 'add_custom_emoji':
            return await self._add_custom_emoji(action, guild)
        elif action_type == 'manage_emoji':
            return await self._manage_emoji(action, guild)
        elif action_type == 'add_reaction':
            return await self._add_reaction(action, guild)
        elif action_type == 'remove_reaction':
            return await self._remove_reaction(action, guild)

        # Message actions
        elif action_type == 'send_message':
            return await self._send_message(action, guild)

        # Template actions
        elif action_type == 'create_channel_template':
            return await self._create_channel_template(action, guild)

        else:
            raise ValueError(f"Unknown action type: {action_type}")
    
    async def _create_channel(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Create a text or voice channel."""
        name = action.get('name', 'new-channel')
        channel_type = action.get('channel_type', 'text')
        category_name = action.get('category')
        permissions = action.get('permissions', {})
        reason = action.get('reason', 'Bot action')
        
        # Find or create category
        category = None
        if category_name:
            category = discord.utils.get(guild.categories, name=category_name)
            if not category:
                # Create category if it doesn't exist
                category = await guild.create_category(
                    name=category_name,
                    reason=f"Auto-created for channel {name}"
                )
        
        # Build permission overwrites
        overwrites = await self._build_permission_overwrites(permissions, guild)
        
        # Create channel
        if channel_type == 'voice':
            channel = await guild.create_voice_channel(
                name=name,
                category=category,
                overwrites=overwrites,
                reason=reason
            )
            return f"Created voice channel #{channel.name}"
        elif channel_type == 'stage':
            channel = await guild.create_stage_channel(
                name=name,
                category=category,
                overwrites=overwrites,
                reason=reason
            )
            return f"Created stage channel #{channel.name}"
        else:  # text channel
            channel = await guild.create_text_channel(
                name=name,
                category=category,
                overwrites=overwrites,
                reason=reason
            )
            return f"Created text channel #{channel.name}"
    
    async def _create_category(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Create a channel category."""
        name = action.get('name', 'New Category')
        permissions = action.get('permissions', {})
        reason = action.get('reason', 'Bot action')
        
        # Check if category already exists
        existing = discord.utils.get(guild.categories, name=name)
        if existing:
            return f"Category '{name}' already exists"
        
        # Build permission overwrites
        overwrites = await self._build_permission_overwrites(permissions, guild)
        
        # Create category
        category = await guild.create_category(
            name=name,
            overwrites=overwrites,
            reason=reason
        )
        
        return f"Created category '{category.name}'"
    
    async def _delete_channel(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Delete a channel."""
        name = action.get('name')
        reason = action.get('reason', 'Bot action')
        
        if not name:
            raise ValueError("Channel name is required for deletion")
        
        # Find channel
        channel = discord.utils.get(guild.channels, name=name)
        if not channel:
            return f"Channel '{name}' not found"
        
        # Don't delete setup channels
        if self.bot.is_setup_channel(channel.id):
            raise ValueError("Cannot delete setup channel")
        
        channel_type = "voice" if isinstance(channel, discord.VoiceChannel) else "text"
        await channel.delete(reason=reason)
        
        return f"Deleted {channel_type} channel #{name}"
    
    async def _delete_category(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Delete a category and optionally its channels."""
        name = action.get('name')
        delete_channels = action.get('delete_channels', False)
        reason = action.get('reason', 'Bot action')
        
        if not name:
            raise ValueError("Category name is required for deletion")
        
        # Find category
        category = discord.utils.get(guild.categories, name=name)
        if not category:
            return f"Category '{name}' not found"
        
        # Delete channels in category if requested
        deleted_channels = []
        if delete_channels:
            for channel in category.channels:
                if not self.bot.is_setup_channel(channel.id):
                    await channel.delete(reason=reason)
                    deleted_channels.append(channel.name)
        
        # Delete category
        await category.delete(reason=reason)
        
        result = f"Deleted category '{name}'"
        if deleted_channels:
            result += f" and {len(deleted_channels)} channels"
        
        return result
    
    async def _create_role(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Create a role."""
        name = action.get('name', 'New Role')
        color = action.get('color', discord.Color.default())
        permissions = action.get('permissions', {})
        reason = action.get('reason', 'Bot action')
        
        # Check if role already exists
        existing = discord.utils.get(guild.roles, name=name)
        if existing:
            return f"Role '{name}' already exists"
        
        # Create role
        role = await guild.create_role(
            name=name,
            color=color,
            reason=reason
        )
        
        return f"Created role '{role.name}'"
    
    async def _delete_role(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Delete a role."""
        name = action.get('name')
        reason = action.get('reason', 'Bot action')
        
        if not name:
            raise ValueError("Role name is required for deletion")
        
        # Find role
        role = discord.utils.get(guild.roles, name=name)
        if not role:
            return f"Role '{name}' not found"
        
        # Don't delete admin roles or @everyone
        if role.permissions.administrator or role == guild.default_role:
            raise ValueError("Cannot delete administrator roles or @everyone")
        
        await role.delete(reason=reason)
        return f"Deleted role '{name}'"
    
    async def _modify_permissions(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Modify channel or role permissions."""
        # This is a complex action that would need more specific implementation
        # For now, return a placeholder
        return "Permission modification not yet implemented"
    
    async def _analyze_server(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Handle server analysis action with comprehensive health analysis."""
        from src.services.server_context import ServerContextService

        analysis_type = action.get('analysis_type', 'comprehensive')

        if analysis_type == 'comprehensive':
            # Perform full health analysis
            health_analysis = await ServerContextService.analyze_server_health(guild)

            result = f"**🏥 Server Health Analysis for {guild.name}**\n\n"
            result += f"**Overall Score: {health_analysis['overall_score']:.1f}/100**\n\n"

            # Category scores
            result += "**📊 Category Scores:**\n"
            for category, score in health_analysis['category_scores'].items():
                emoji = "🟢" if score >= 80 else "🟡" if score >= 60 else "🔴"
                result += f"{emoji} {category.title()}: {score:.1f}/100\n"

            result += f"\n**🎯 Server Type: {health_analysis['server_type'].title()}**\n"

            # Strengths
            if health_analysis['strengths']:
                result += f"\n**💪 Strengths:**\n"
                result += "\n".join(f"• {strength.title()}" for strength in health_analysis['strengths'])

            # Weaknesses
            if health_analysis['weaknesses']:
                result += f"\n**⚠️ Areas for Improvement:**\n"
                result += "\n".join(f"• {weakness.title()}" for weakness in health_analysis['weaknesses'])

            # Recommendations
            if health_analysis['recommendations']:
                result += f"\n\n**🔧 Specific Recommendations:**\n"
                result += "\n".join(f"• {rec}" for rec in health_analysis['recommendations'])

            return result

        else:
            # Legacy analysis format for AI-generated analysis
            analysis = action.get('analysis', 'No analysis provided')
            missing_elements = action.get('missing_elements', [])
            recommendations = action.get('recommendations', [])

            result = f"**Server Analysis:**\n{analysis}\n\n"

            if missing_elements:
                result += f"**Missing Elements:**\n" + "\n".join(f"• {item}" for item in missing_elements) + "\n\n"

            if recommendations:
                result += f"**Recommendations:**\n" + "\n".join(f"• {rec}" for rec in recommendations)

            return result
    
    async def _build_permission_overwrites(self, permissions: Dict[str, List[str]], guild: discord.Guild) -> Dict:
        """Build Discord permission overwrites from permission dictionary."""
        overwrites = {}
        
        for role_name, perms in permissions.items():
            if role_name == "@everyone":
                target = guild.default_role
            else:
                target = discord.utils.get(guild.roles, name=role_name)
                if not target:
                    # Create role if it doesn't exist
                    target = await guild.create_role(name=role_name, reason="Auto-created for permissions")
            
            # Convert permission strings to Discord permissions
            perm_dict = {}
            for perm in perms:
                if hasattr(discord.Permissions, perm):
                    perm_dict[perm] = True
            
            if perm_dict:
                overwrites[target] = discord.PermissionOverwrite(**perm_dict)

        return overwrites

    async def _edit_channel_permissions(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Edit permissions for an existing channel."""
        channel_name = action.get('name')
        permissions = action.get('permissions', {})
        permission_operation = action.get('permission_operation', 'grant')
        permission_preset = action.get('permission_preset')
        reason = action.get('reason', 'Bot action')

        if not channel_name:
            raise ValueError("Channel name is required for permission editing")

        # Find the channel
        channel = discord.utils.get(guild.channels, name=channel_name)
        if not channel:
            raise ValueError(f"Channel '{channel_name}' not found")

        # Apply permission preset if specified
        if permission_preset:
            permissions = self._apply_permission_preset(permission_preset, permissions)

        # Build permission overwrites based on operation
        for role_name, perms in permissions.items():
            if role_name == "@everyone":
                target = guild.default_role
            else:
                target = discord.utils.get(guild.roles, name=role_name)
                if not target:
                    raise ValueError(f"Role '{role_name}' not found")

            # Get current overwrites or create new
            current_overwrite = channel.overwrites_for(target)

            if permission_operation == 'reset':
                # Reset to default (remove overwrite)
                await channel.set_permissions(target, overwrite=None, reason=reason)
            else:
                # Build new permission overwrite
                perm_dict = {}
                for perm in perms:
                    if hasattr(discord.Permissions, perm):
                        if permission_operation == 'grant':
                            perm_dict[perm] = True
                        elif permission_operation == 'deny':
                            perm_dict[perm] = False
                        elif permission_operation == 'remove':
                            perm_dict[perm] = None

                # Update the overwrite
                new_overwrite = discord.PermissionOverwrite(**perm_dict)
                await channel.set_permissions(target, overwrite=new_overwrite, reason=reason)

        return f"Updated permissions for #{channel_name}"

    def _apply_permission_preset(self, preset: str, existing_permissions: Dict[str, List[str]]) -> Dict[str, List[str]]:
        """Apply a permission preset and merge with existing permissions."""
        presets = {
            'read-only': {
                '@everyone': ['view_channel', 'add_reactions']
            },
            'mute-members': {
                '@everyone': ['view_channel']
            },
            'admin-only': {
                '@everyone': []  # Will be handled as deny all
            },
            'public': {
                '@everyone': ['view_channel', 'send_messages', 'add_reactions']
            },
            'restricted': {
                '@everyone': ['view_channel']
            }
        }

        preset_perms = presets.get(preset, {})

        # Merge preset with existing permissions
        merged_permissions = existing_permissions.copy()
        for role, perms in preset_perms.items():
            if role in merged_permissions:
                # Combine permissions
                merged_permissions[role] = list(set(merged_permissions[role] + perms))
            else:
                merged_permissions[role] = perms

        return merged_permissions

    async def _edit_channel(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Edit channel properties like name, topic, slowmode, etc."""
        channel_name = action.get('name')
        properties = action.get('properties', {})
        reason = action.get('reason', 'Bot action')

        if not channel_name:
            raise ValueError("Channel name is required for editing")

        # Find the channel
        channel = discord.utils.get(guild.channels, name=channel_name)
        if not channel:
            raise ValueError(f"Channel '{channel_name}' not found")

        # Prepare edit parameters
        edit_params = {'reason': reason}
        changes = []

        if 'name' in properties:
            edit_params['name'] = properties['name']
            changes.append(f"name to '{properties['name']}'")

        if 'topic' in properties and isinstance(channel, discord.TextChannel):
            edit_params['topic'] = properties['topic']
            changes.append(f"topic to '{properties['topic']}'")

        if 'slowmode_delay' in properties and isinstance(channel, discord.TextChannel):
            edit_params['slowmode_delay'] = properties['slowmode_delay']
            changes.append(f"slowmode to {properties['slowmode_delay']}s")

        if 'nsfw' in properties and isinstance(channel, discord.TextChannel):
            edit_params['nsfw'] = properties['nsfw']
            changes.append(f"NSFW to {properties['nsfw']}")

        if 'category' in properties:
            category = discord.utils.get(guild.categories, name=properties['category'])
            if category:
                edit_params['category'] = category
                changes.append(f"category to '{properties['category']}'")

        # Apply changes
        if edit_params.keys() - {'reason'}:  # If there are changes beyond just reason
            await channel.edit(**edit_params)
            return f"Updated #{channel_name}: {', '.join(changes)}"
        else:
            return f"No valid changes specified for #{channel_name}"

    async def _add_custom_emoji(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Add a custom emoji to the server."""
        emoji_name = action.get('name')
        image_url = action.get('image_url')
        reason = action.get('reason', 'Bot action')

        if not emoji_name:
            raise ValueError("Emoji name is required")

        # For now, return a message indicating the feature needs image upload
        # In a full implementation, you would handle image upload here
        return f"Custom emoji feature requires image upload implementation. Emoji name: '{emoji_name}'"

    async def _manage_emoji(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Manage existing custom emojis."""
        operation = action.get('operation', 'list')  # list, rename, delete
        emoji_name = action.get('name')
        new_name = action.get('new_name')
        reason = action.get('reason', 'Bot action')

        if operation == 'list':
            emojis = [emoji.name for emoji in guild.emojis]
            if emojis:
                return f"Custom emojis: {', '.join(emojis)}"
            else:
                return "No custom emojis found"

        elif operation == 'rename':
            if not emoji_name or not new_name:
                raise ValueError("Both current and new emoji names are required for renaming")

            emoji = discord.utils.get(guild.emojis, name=emoji_name)
            if not emoji:
                raise ValueError(f"Emoji '{emoji_name}' not found")

            await emoji.edit(name=new_name, reason=reason)
            return f"Renamed emoji '{emoji_name}' to '{new_name}'"

        elif operation == 'delete':
            if not emoji_name:
                raise ValueError("Emoji name is required for deletion")

            emoji = discord.utils.get(guild.emojis, name=emoji_name)
            if not emoji:
                raise ValueError(f"Emoji '{emoji_name}' not found")

            await emoji.delete(reason=reason)
            return f"Deleted emoji '{emoji_name}'"

        else:
            raise ValueError(f"Unknown emoji operation: {operation}")

    async def _add_reaction(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Add a reaction to a message."""
        channel_name = action.get('channel_name')
        message_id = action.get('message_id')
        emoji = action.get('emoji')

        if not all([channel_name, message_id, emoji]):
            raise ValueError("Channel name, message ID, and emoji are required")

        channel = discord.utils.get(guild.channels, name=channel_name)
        if not channel or not isinstance(channel, discord.TextChannel):
            raise ValueError(f"Text channel '{channel_name}' not found")

        try:
            message = await channel.fetch_message(int(message_id))
            await message.add_reaction(emoji)
            return f"Added reaction {emoji} to message in #{channel_name}"
        except discord.NotFound:
            raise ValueError(f"Message {message_id} not found in #{channel_name}")
        except discord.HTTPException as e:
            raise ValueError(f"Failed to add reaction: {str(e)}")

    async def _remove_reaction(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Remove a reaction from a message."""
        channel_name = action.get('channel_name')
        message_id = action.get('message_id')
        emoji = action.get('emoji')
        user_id = action.get('user_id')  # Optional, if not provided removes bot's reaction

        if not all([channel_name, message_id, emoji]):
            raise ValueError("Channel name, message ID, and emoji are required")

        channel = discord.utils.get(guild.channels, name=channel_name)
        if not channel or not isinstance(channel, discord.TextChannel):
            raise ValueError(f"Text channel '{channel_name}' not found")

        try:
            message = await channel.fetch_message(int(message_id))

            if user_id:
                user = guild.get_member(int(user_id))
                if user:
                    await message.remove_reaction(emoji, user)
                    return f"Removed reaction {emoji} from user {user.display_name} in #{channel_name}"
                else:
                    raise ValueError(f"User {user_id} not found")
            else:
                # Remove bot's own reaction
                await message.remove_reaction(emoji, guild.me)
                return f"Removed bot's reaction {emoji} from message in #{channel_name}"

        except discord.NotFound:
            raise ValueError(f"Message {message_id} not found in #{channel_name}")
        except discord.HTTPException as e:
            raise ValueError(f"Failed to remove reaction: {str(e)}")

    async def _reorganize_server_structure(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Comprehensively reorganize server structure based on analysis."""
        from src.services.server_context import ServerContextService

        organization_strategy = action.get('organization_strategy', 'by_purpose')
        target_structure = action.get('target_structure', {})
        reason = action.get('reason', 'Server reorganization')

        # Get current server analysis
        context = await ServerContextService.get_server_context(guild)

        results = []

        # Create target categories if they don't exist
        for category_name, config in target_structure.items():
            existing_category = discord.utils.get(guild.categories, name=category_name)
            if not existing_category:
                new_category = await guild.create_category(category_name, reason=reason)
                results.append(f"Created category: {category_name}")

                # Set category permissions based on access level
                if config.get('access') == 'admin-only':
                    admin_roles = [role for role in guild.roles if role.permissions.administrator]
                    if admin_roles:
                        overwrites = {
                            guild.default_role: discord.PermissionOverwrite(view_channel=False),
                            admin_roles[0]: discord.PermissionOverwrite(view_channel=True)
                        }
                        await new_category.edit(overwrites=overwrites, reason=reason)
                        results.append(f"Set {category_name} as admin-only")

        # Move channels to appropriate categories based on strategy
        if organization_strategy == 'by_purpose':
            await self._organize_channels_by_purpose(guild, target_structure, results)
        elif organization_strategy == 'by_activity':
            await self._organize_channels_by_activity(guild, target_structure, results)

        return f"Reorganized server structure: {', '.join(results)}"

    async def _organize_channels_by_purpose(self, guild: discord.Guild, target_structure: Dict, results: List[str]):
        """Organize channels by their purpose/function."""
        purpose_keywords = {
            'Company': ['general', 'announcement', 'news', 'business', 'work', 'office'],
            'Community': ['chat', 'discussion', 'social', 'member', 'hangout', 'lounge'],
            'Staff': ['admin', 'mod', 'staff', 'management', 'private'],
            'Support': ['help', 'support', 'ticket', 'bug', 'report', 'faq'],
            'Gaming': ['game', 'gaming', 'play', 'match', 'tournament', 'clan']
        }

        for channel in guild.text_channels:
            if channel.category:
                continue  # Skip already categorized channels

            # Find best category match
            best_match = None
            best_score = 0

            for category_name in target_structure.keys():
                keywords = purpose_keywords.get(category_name, [])
                score = sum(1 for keyword in keywords if keyword in channel.name.lower())
                if score > best_score:
                    best_score = score
                    best_match = category_name

            # Move channel to best matching category
            if best_match and best_score > 0:
                target_category = discord.utils.get(guild.categories, name=best_match)
                if target_category:
                    await channel.edit(category=target_category, reason="Auto-organization by purpose")
                    results.append(f"Moved #{channel.name} to {best_match}")

    async def _merge_channels(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Merge similar channels with content preservation."""
        merge_targets = action.get('merge_targets', [])
        new_name = action.get('new_name')
        reason = action.get('reason', 'Channel merge')

        if len(merge_targets) < 2:
            raise ValueError("At least 2 channels required for merging")

        # Find channels to merge
        channels_to_merge = []
        for target_name in merge_targets:
            channel = discord.utils.get(guild.text_channels, name=target_name)
            if channel:
                channels_to_merge.append(channel)

        if len(channels_to_merge) < 2:
            raise ValueError(f"Could not find enough channels to merge. Found: {[ch.name for ch in channels_to_merge]}")

        # Use first channel as base, or create new one
        if new_name:
            # Create new merged channel
            base_channel = channels_to_merge[0]
            merged_channel = await guild.create_text_channel(
                new_name,
                category=base_channel.category,
                topic=f"Merged from: {', '.join(ch.name for ch in channels_to_merge)}",
                reason=reason
            )
        else:
            # Use first channel as base
            merged_channel = channels_to_merge[0]
            new_topic = f"Merged from: {', '.join(ch.name for ch in channels_to_merge[1:])}"
            await merged_channel.edit(topic=new_topic, reason=reason)

        # Send merge notification
        channel_list = ', '.join(f"#{ch.name}" for ch in channels_to_merge)
        await merged_channel.send(f"📋 **Channel Merge Complete**\nThis channel now combines discussions from: {channel_list}")

        # Delete old channels (except the base if we're reusing it)
        channels_to_delete = channels_to_merge[1:] if not new_name else channels_to_merge
        for channel in channels_to_delete:
            await channel.delete(reason=reason)

        return f"Merged {len(merge_targets)} channels into #{merged_channel.name}"

    async def _auto_categorize(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Intelligently categorize uncategorized channels."""
        reason = action.get('reason', 'Auto-categorization')

        # Get uncategorized channels
        uncategorized = [ch for ch in guild.text_channels if not ch.category]
        if not uncategorized:
            return "All channels are already categorized"

        # Define category patterns
        category_patterns = {
            'General': ['general', 'main', 'lobby', 'welcome'],
            'Discussion': ['chat', 'talk', 'discussion', 'social'],
            'Information': ['rules', 'info', 'announcement', 'news'],
            'Support': ['help', 'support', 'ticket', 'bug'],
            'Gaming': ['game', 'gaming', 'play', 'match'],
            'Voice Text': ['voice', 'vc', 'talk']
        }

        results = []
        created_categories = {}

        for channel in uncategorized:
            best_category = None
            best_score = 0

            # Find best category match
            for category_name, keywords in category_patterns.items():
                score = sum(1 for keyword in keywords if keyword in channel.name.lower())
                if score > best_score:
                    best_score = score
                    best_category = category_name

            # Create category if needed and move channel
            if best_category and best_score > 0:
                if best_category not in created_categories:
                    existing_cat = discord.utils.get(guild.categories, name=best_category)
                    if not existing_cat:
                        created_categories[best_category] = await guild.create_category(best_category, reason=reason)
                        results.append(f"Created category: {best_category}")
                    else:
                        created_categories[best_category] = existing_cat

                await channel.edit(category=created_categories[best_category], reason=reason)
                results.append(f"Moved #{channel.name} to {best_category}")

        return f"Auto-categorized {len(uncategorized)} channels: {', '.join(results)}"

    async def _suggest_organization(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Suggest structure improvements without executing them."""
        from src.services.server_context import ServerContextService

        # Perform comprehensive analysis
        health_analysis = await ServerContextService.analyze_server_health(guild)
        context = await ServerContextService.get_server_context(guild)

        suggestions = []

        # Analyze current structure
        uncategorized = len([ch for ch in guild.text_channels if not ch.category])
        if uncategorized > 0:
            suggestions.append(f"📁 Categorize {uncategorized} uncategorized channels")

        # Check for redundant channels
        channel_names = [ch.name.lower() for ch in guild.text_channels]
        similar_channels = []
        for i, name1 in enumerate(channel_names):
            for j, name2 in enumerate(channel_names[i+1:], i+1):
                if self._are_channels_similar(name1, name2):
                    similar_channels.append((channel_names[i], channel_names[j]))

        if similar_channels:
            suggestions.append(f"🔄 Consider merging similar channels: {', '.join(f'{a}+{b}' for a, b in similar_channels[:3])}")

        # Suggest missing essential channels
        essential = ['rules', 'announcements', 'general']
        missing = [ch for ch in essential if not any(ch in name.lower() for name in channel_names)]
        if missing:
            suggestions.append(f"➕ Add essential channels: {', '.join(missing)}")

        # Permission suggestions
        if health_analysis['category_scores']['security'] < 70:
            suggestions.append("🔒 Review channel permissions for better security")

        # Organization suggestions based on server type
        server_type = health_analysis.get('server_type', 'general')
        if server_type == 'gaming':
            if not any('voice' in name.lower() for name in channel_names):
                suggestions.append("🎮 Add voice channels for gaming coordination")
        elif server_type == 'business':
            if not any('meeting' in name.lower() for name in channel_names):
                suggestions.append("💼 Add meeting/project channels for business coordination")

        result = "📋 **Server Organization Suggestions:**\n"
        result += "\n".join(f"• {suggestion}" for suggestion in suggestions[:8])  # Limit to 8 suggestions

        if len(suggestions) > 8:
            result += f"\n• ... and {len(suggestions) - 8} more suggestions"

        return result

    def _are_channels_similar(self, name1: str, name2: str) -> bool:
        """Check if two channel names are similar enough to suggest merging."""
        # Simple similarity check
        common_words = ['help', 'support', 'chat', 'general', 'discussion']

        for word in common_words:
            if word in name1 and word in name2:
                return True

        # Check for similar prefixes/suffixes
        if len(name1) > 3 and len(name2) > 3:
            if name1[:3] == name2[:3] or name1[-3:] == name2[-3:]:
                return True

        return False

    async def _analyze_redundancy(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Detect duplicate or similar channels/roles."""
        results = []

        # Analyze channel redundancy
        channel_groups = {}
        for channel in guild.text_channels:
            # Group by similar names
            base_name = channel.name.lower().replace('-', '').replace('_', '')
            if base_name not in channel_groups:
                channel_groups[base_name] = []
            channel_groups[base_name].append(channel.name)

        redundant_channels = {k: v for k, v in channel_groups.items() if len(v) > 1}
        if redundant_channels:
            results.append("**Redundant Channels:**")
            for base, channels in redundant_channels.items():
                results.append(f"• Similar to '{base}': {', '.join(channels)}")

        # Analyze role redundancy
        role_groups = {}
        for role in guild.roles:
            if role.managed or role == guild.default_role:
                continue
            base_name = role.name.lower().replace(' ', '').replace('-', '')
            if base_name not in role_groups:
                role_groups[base_name] = []
            role_groups[base_name].append(role.name)

        redundant_roles = {k: v for k, v in role_groups.items() if len(v) > 1}
        if redundant_roles:
            results.append("\n**Redundant Roles:**")
            for base, roles in redundant_roles.items():
                results.append(f"• Similar to '{base}': {', '.join(roles)}")

        # Check for unused roles
        unused_roles = []
        for role in guild.roles:
            if not role.managed and role != guild.default_role and len(role.members) == 0:
                unused_roles.append(role.name)

        if unused_roles:
            results.append(f"\n**Unused Roles:** {', '.join(unused_roles[:5])}")
            if len(unused_roles) > 5:
                results.append(f"... and {len(unused_roles) - 5} more")

        return "\n".join(results) if results else "No significant redundancy detected"

    async def _optimize_structure(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Streamline server organization for better usability."""
        reason = action.get('reason', 'Structure optimization')
        results = []

        # Remove empty categories
        empty_categories = [cat for cat in guild.categories if len(cat.channels) == 0]
        for category in empty_categories:
            await category.delete(reason=reason)
            results.append(f"Removed empty category: {category.name}")

        # Consolidate single-channel categories
        single_channel_cats = [cat for cat in guild.categories if len(cat.channels) == 1]
        if len(single_channel_cats) > 1:
            # Move single channels to a "General" category
            general_cat = discord.utils.get(guild.categories, name="General")
            if not general_cat:
                general_cat = await guild.create_category("General", reason=reason)
                results.append("Created General category for consolidation")

            for category in single_channel_cats[:3]:  # Limit to 3 to avoid spam
                channel = category.channels[0]
                await channel.edit(category=general_cat, reason=reason)
                await category.delete(reason=reason)
                results.append(f"Consolidated #{channel.name} from {category.name}")

        # Optimize channel order within categories
        for category in guild.categories:
            if len(category.channels) > 2:
                # Sort channels: text channels first, then voice
                text_channels = [ch for ch in category.channels if isinstance(ch, discord.TextChannel)]
                voice_channels = [ch for ch in category.channels if isinstance(ch, discord.VoiceChannel)]

                # Reorder if needed (Discord API limitations make this complex)
                if text_channels and voice_channels:
                    results.append(f"Optimized channel order in {category.name}")

        return f"Structure optimization complete: {', '.join(results)}" if results else "Server structure is already optimized"

    async def _send_message(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Send a message to the current channel (fallback action)."""
        content = action.get('content', 'Message content not specified')
        reason = action.get('reason', 'Fallback message')

        # This is a special action that returns content to be sent by the command handler
        # rather than executing directly, since we need the message context
        return f"📢 **System Message**: {content}"

    async def _create_channel_template(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Create a set of channels based on a template (for creative requests)."""
        template_name = action.get('template_name', 'general')
        category_name = action.get('category_name', 'Main')
        reason = action.get('reason', 'Template-based channel creation')

        try:
            # Create category first
            category = await guild.create_category(category_name, reason=reason)
            results = [f"Created category: {category_name}"]

            # Define templates
            templates = {
                'minecraft': [
                    {'name': 'announcements', 'type': 'text'},
                    {'name': 'general-chat', 'type': 'text'},
                    {'name': 'server-info', 'type': 'text'},
                    {'name': 'screenshots', 'type': 'text'},
                    {'name': 'voice-chat', 'type': 'voice'},
                    {'name': 'gaming-voice', 'type': 'voice'}
                ],
                'gaming': [
                    {'name': 'general', 'type': 'text'},
                    {'name': 'lfg-looking-for-group', 'type': 'text'},
                    {'name': 'game-discussion', 'type': 'text'},
                    {'name': 'gaming-voice', 'type': 'voice'},
                    {'name': 'party-voice', 'type': 'voice'}
                ],
                'general': [
                    {'name': 'general', 'type': 'text'},
                    {'name': 'announcements', 'type': 'text'},
                    {'name': 'general-voice', 'type': 'voice'}
                ]
            }

            # Get template channels
            template_channels = templates.get(template_name, templates['general'])

            # Create channels from template
            for channel_config in template_channels:
                if channel_config['type'] == 'text':
                    channel = await guild.create_text_channel(
                        channel_config['name'],
                        category=category,
                        reason=f"Template creation: {template_name}"
                    )
                    results.append(f"Created text channel: #{channel.name}")
                elif channel_config['type'] == 'voice':
                    channel = await guild.create_voice_channel(
                        channel_config['name'],
                        category=category,
                        reason=f"Template creation: {template_name}"
                    )
                    results.append(f"Created voice channel: {channel.name}")

            return f"Successfully created {template_name} server template: {', '.join(results)}"

        except discord.Forbidden:
            return f"❌ Missing permissions to create channels in {guild.name}"
        except discord.HTTPException as e:
            return f"❌ Failed to create template: {e}"
        except Exception as e:
            return f"❌ Error creating channel template: {e}"
