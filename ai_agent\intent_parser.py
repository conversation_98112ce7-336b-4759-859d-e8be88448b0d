"""
Three-stage processing pipeline: classify → detect → respond for AgenticMod.
"""
import json
import time
import asyncio
from typing import Dict, List, Any, Optional
from groq import Groq
import logging
from config.settings import (
    GROQ_API_KEY, GROQ_MODEL_CONFIGS, DEFAULT_MODELS,
    GROQ_RATE_LIMIT_DELAY, MAX_RETRIES, RETRY_DELAY
)

logger = logging.getLogger(__name__)

class IntentParser:
    """Three-stage AI processing pipeline for Discord server management commands."""
    
    def __init__(self):
        self.client = Groq(api_key=GROQ_API_KEY)
        self.model_configs = GROQ_MODEL_CONFIGS
        self.default_models = DEFAULT_MODELS
        
        # Stage-specific prompts
        self.classification_prompt = self._get_classification_prompt()
        self.detection_prompts = self._get_detection_prompts()
        self.response_prompts = self._get_response_prompts()
        
        logger.info("IntentParser initialized with three-stage pipeline")
    
    async def parse_intent(self, user_input: str, server_context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        THREE-STAGE AI PROCESSING PIPELINE:
        Stage 1: Request Classification (SIMPLE/COMPLEX/CREATIVE)
        Stage 2: Adaptive Intent Detection (classification-specific processing)
        Stage 3: Tailored Response Generation (depth appropriate to request type)
        """
        pipeline_start_time = time.time()
        
        try:
            if server_context is None:
                server_context = {}
            
            # STAGE 1: REQUEST CLASSIFICATION
            classification = await self.classify_request(user_input)
            classification_type = classification["classification"]
            confidence_score = classification["confidence_score"]
            
            logger.info(f"🎯 STAGE 1: Classified as {classification_type} (confidence: {confidence_score:.2f})")
            
            # STAGE 2: ADAPTIVE INTENT DETECTION
            if classification_type == "SIMPLE" and confidence_score >= 0.8:
                logger.info("🚀 STAGE 2: Processing with SIMPLE pipeline")
                result = await self.process_simple_request(user_input, server_context)
            elif classification_type == "COMPLEX":
                logger.info("🔧 STAGE 2: Processing with COMPLEX pipeline")
                result = await self.process_complex_request(user_input, server_context)
            else:  # CREATIVE
                logger.info("🎨 STAGE 2: Processing with CREATIVE pipeline")
                result = await self.process_creative_request(user_input, server_context)
            
            # STAGE 3: RESPONSE GENERATION (integrated into stage 2 results)
            result['classification'] = classification
            result['pipeline_time'] = time.time() - pipeline_start_time
            
            logger.info(f"✅ Three-stage pipeline completed in {result['pipeline_time']:.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"Error in three-stage pipeline: {e}", exc_info=True)
            return self._get_fallback_response(user_input)
    
    async def classify_request(self, user_input: str) -> Dict[str, Any]:
        """
        Stage 1: Classify user request into SIMPLE, COMPLEX, or CREATIVE categories.
        """
        try:
            start_time = time.time()
            logger.debug(f"Classifying request: {user_input[:100]}...")
            
            classification_prompt = f"{self.classification_prompt}\n\nUser Request: {user_input}"
            
            response = await self._generate_response(
                classification_prompt, 
                self.default_models['classification']
            )
            
            parsing_time = time.time() - start_time
            
            try:
                result = json.loads(response)
                result['parsing_time'] = parsing_time
                return result
            except json.JSONDecodeError:
                logger.warning("Failed to parse classification JSON, using fallback")
                return {
                    "classification": "COMPLEX",
                    "confidence_score": 0.5,
                    "reasoning": "JSON parsing failed, defaulting to COMPLEX",
                    "parsing_time": parsing_time
                }
                
        except Exception as e:
            logger.error(f"Error in classification: {e}")
            return {
                "classification": "COMPLEX",
                "confidence_score": 0.3,
                "reasoning": f"Classification error: {e}",
                "parsing_time": 0.0
            }
    
    async def process_simple_request(self, user_input: str, server_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Stage 2A: Process SIMPLE requests with lightweight pattern matching.
        Target: <0.5 seconds response time.
        """
        start_time = time.time()
        logger.debug("Processing SIMPLE request with lightweight parsing")
        
        # Try local pattern matching first
        local_result = self._parse_simple_patterns(user_input, server_context)
        if local_result:
            processing_time = time.time() - start_time
            logger.info(f"SIMPLE request processed locally in {processing_time:.3f}s")
            return local_result
        
        # Fallback to minimal AI processing
        simple_prompt = f"""
Parse this simple Discord server management request and respond with JSON:

Request: {user_input}

Respond with:
{{
  "actions": [{{
    "type": "action_type",
    "name": "target_name",
    "reason": "brief_reason"
  }}],
  "summary": "Brief action description",
  "intents": [{{"intent_id": "simple_1", "description": "action description", "priority": 1}}]
}}
"""
        
        try:
            response = await self._generate_response(simple_prompt, self.default_models['simple_processing'])
            result = json.loads(response)
            
            processing_time = time.time() - start_time
            result['processing_time'] = processing_time
            result['stage'] = 'SIMPLE'
            
            logger.info(f"SIMPLE request processed with AI in {processing_time:.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"Error processing simple request: {e}")
            return self._get_fallback_response(user_input)
    
    async def process_complex_request(self, user_input: str, server_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Stage 2B: Process COMPLEX requests with multi-intent parsing.
        Target: <2 seconds response time.
        """
        start_time = time.time()
        logger.debug("Processing COMPLEX request with multi-intent parsing")
        
        complex_prompt = f"""
You are an advanced AI assistant that converts complex, multi-step Discord server management commands into structured JSON actions.

CAPABILITIES:
- Channel Management: Create, delete, modify text/voice channels
- Role Management: Create roles, assign permissions, manage hierarchy
- Category Organization: Create categories, organize channels
- Permission Systems: Set granular permissions for roles and channels
- Server Analysis: Analyze current server structure and suggest improvements

USER COMMAND: {user_input}

SERVER CONTEXT: {json.dumps(server_context, indent=2)}

Respond with a detailed JSON structure:
{{
  "intents": [
    {{
      "intent_id": "unique_id",
      "description": "What this intent accomplishes",
      "priority": 1,
      "dependencies": ["other_intent_ids"],
      "actions": [
        {{
          "type": "create_channel|create_role|modify_permissions|etc",
          "parameters": {{"name": "value", "permissions": {{}}}},
          "validation": {{"required_permissions": ["manage_channels"]}},
          "rollback_info": {{"action": "delete_channel", "target": "created_id"}}
        }}
      ]
    }}
  ],
  "execution_plan": {{
    "total_steps": 3,
    "estimated_time": "5-10 seconds",
    "requires_confirmation": false,
    "risk_level": "low|medium|high"
  }},
  "summary": "Brief description of what will be accomplished"
}}
"""
        
        try:
            response = await self._generate_response(complex_prompt, self.default_models['complex_processing'])
            result = json.loads(response)
            
            processing_time = time.time() - start_time
            result['processing_time'] = processing_time
            result['stage'] = 'COMPLEX'
            
            logger.info(f"COMPLEX request processed in {processing_time:.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"Error processing complex request: {e}")
            return self._get_fallback_response(user_input)
    
    async def process_creative_request(self, user_input: str, server_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Stage 2C: Process CREATIVE requests with design thinking.
        Target: <3 seconds response time.
        """
        start_time = time.time()
        logger.debug("Processing CREATIVE request with design thinking")
        
        creative_prompt = f"""
You are a creative Discord server design expert. Analyze the request and create an innovative, well-structured server layout.

USER REQUEST: {user_input}

CURRENT SERVER STATE: {json.dumps(server_context, indent=2)}

Think creatively about:
1. Server purpose and community needs
2. Optimal channel organization and flow
3. Role hierarchy and permission structure
4. User experience and engagement
5. Scalability and future growth

Respond with:
{{
  "design_concept": {{
    "theme": "Overall server theme/purpose",
    "target_audience": "Who this serves",
    "key_features": ["feature1", "feature2"],
    "design_principles": ["principle1", "principle2"]
  }},
  "implementation_plan": {{
    "categories": [
      {{
        "name": "Category Name",
        "purpose": "Why this category exists",
        "channels": [
          {{"name": "channel-name", "type": "text|voice", "purpose": "Channel purpose"}}
        ]
      }}
    ],
    "roles": [
      {{
        "name": "Role Name",
        "permissions": ["permission1"],
        "purpose": "Role purpose",
        "color": "#hex_color"
      }}
    ]
  }},
  "execution_steps": [
    {{
      "step": 1,
      "action": "create_category",
      "details": "Step details",
      "estimated_time": "30 seconds"
    }}
  ],
  "recommendations": [
    "Additional suggestions for server improvement"
  ]
}}
"""
        
        try:
            response = await self._generate_response(creative_prompt, self.default_models['creative_processing'])
            result = json.loads(response)
            
            processing_time = time.time() - start_time
            result['processing_time'] = processing_time
            result['stage'] = 'CREATIVE'
            
            logger.info(f"CREATIVE request processed in {processing_time:.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"Error processing creative request: {e}")
            return self._get_fallback_response(user_input)
    
    def _parse_simple_patterns(self, user_input: str, server_context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Local pattern matching for common simple actions."""
        text_lower = user_input.lower()
        
        # Simple channel creation patterns
        if 'create' in text_lower and 'channel' in text_lower:
            # Extract channel name (basic pattern)
            words = user_input.split()
            channel_name = None
            for i, word in enumerate(words):
                if word.lower() in ['channel', 'called', 'named'] and i + 1 < len(words):
                    channel_name = words[i + 1].strip('"\'')
                    break
            
            if not channel_name:
                channel_name = "new-channel"
            
            return {
                "actions": [{
                    "type": "create_channel",
                    "name": channel_name,
                    "reason": "User requested channel creation"
                }],
                "summary": f"Create channel '{channel_name}'",
                "intents": [{
                    "intent_id": "simple_create_channel",
                    "description": f"Create text channel named '{channel_name}'",
                    "priority": 1
                }],
                "stage": "SIMPLE_LOCAL"
            }
        
        return None
    
    def _get_classification_prompt(self) -> str:
        """Get the classification prompt for Stage 1."""
        return """
You are a Discord server management request classifier. Analyze the user's request and classify it into exactly one of three categories:

SIMPLE: Direct, single-action requests with clear intent
- Examples: "create a general chat channel", "delete the old-announcements channel", "add a moderator role"
- Characteristics: One clear action, specific target, minimal complexity

COMPLEX: Multi-step requests requiring coordination of several actions
- Examples: "set up a moderation system", "create a gaming category with voice channels", "organize channels by topic"
- Characteristics: Multiple related actions, dependencies between steps, system-level changes

CREATIVE: Open-ended requests requiring server analysis and intelligent design
- Examples: "design a discord server for a minecraft SMP", "reorganize my server to be more professional", "suggest improvements"
- Characteristics: Requires creativity, server analysis, design thinking, recommendations

Respond with ONLY a JSON object:
{
  "classification": "SIMPLE|COMPLEX|CREATIVE",
  "confidence_score": 0.0-1.0,
  "reasoning": "Brief explanation of classification decision"
}
"""
    
    def _get_detection_prompts(self) -> Dict[str, str]:
        """Get detection prompts for Stage 2."""
        return {
            'simple': "Parse simple Discord command",
            'complex': "Parse complex multi-step Discord command",
            'creative': "Design creative Discord server solution"
        }
    
    def _get_response_prompts(self) -> Dict[str, str]:
        """Get response prompts for Stage 3."""
        return {
            'simple': "Generate concise response",
            'complex': "Generate detailed execution plan",
            'creative': "Generate comprehensive design proposal"
        }
    
    async def _generate_response(self, prompt: str, model: str) -> str:
        """Generate response from Groq API with error handling."""
        config = self.model_configs.get(model, self.model_configs[self.default_models['classification']])
        
        for attempt in range(MAX_RETRIES):
            try:
                await asyncio.sleep(GROQ_RATE_LIMIT_DELAY)
                
                response = self.client.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=config['max_tokens'],
                    temperature=config['temperature'],
                    top_p=0.9
                )
                
                if response and response.choices:
                    return response.choices[0].message.content.strip()
                else:
                    raise Exception("Empty response from Groq API")
                    
            except Exception as e:
                logger.warning(f"Groq API attempt {attempt + 1} failed: {e}")
                if attempt < MAX_RETRIES - 1:
                    await asyncio.sleep(RETRY_DELAY * (attempt + 1))
                else:
                    raise
        
        raise Exception("All Groq API attempts failed")
    
    def _get_fallback_response(self, user_input: str) -> Dict[str, Any]:
        """Get a fallback response when processing fails."""
        return {
            "actions": [],
            "summary": "Unable to process request",
            "intents": [{
                "intent_id": "fallback",
                "description": "Request processing failed",
                "priority": 1
            }],
            "error": "Processing failed",
            "stage": "FALLBACK",
            "processing_time": 0.0
        }
