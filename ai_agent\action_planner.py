"""
Action planning with dependency resolution for AgenticMod.
"""
import json
from typing import Dict, List, Any, Optional, Tuple
import logging
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ActionStatus(Enum):
    """Status of an action in the execution plan."""
    PENDING = "pending"
    READY = "ready"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

@dataclass
class PlannedAction:
    """Represents a planned action with dependencies and metadata."""
    action_id: str
    tool_name: str
    parameters: Dict[str, Any]
    dependencies: List[str]
    priority: int
    status: ActionStatus = ActionStatus.PENDING
    estimated_time: float = 1.0
    risk_level: str = "low"
    rollback_required: bool = True
    description: str = ""

class ActionPlanner:
    """Plans and manages multi-step action sequences with dependency resolution."""
    
    def __init__(self):
        self.available_tools = {
            'create_channel': {
                'provides': ['channel_id'],
                'requires': ['guild_context'],
                'estimated_time': 2.0
            },
            'create_role': {
                'provides': ['role_id'],
                'requires': ['guild_context'],
                'estimated_time': 1.5
            },
            'assign_role': {
                'provides': ['role_assignment'],
                'requires': ['guild_context', 'role_id'],
                'estimated_time': 1.0
            },
            'analyze_server': {
                'provides': ['server_analysis'],
                'requires': ['guild_context'],
                'estimated_time': 3.0
            },
            'delete_channel': {
                'provides': ['channel_deleted'],
                'requires': ['guild_context', 'channel_id'],
                'estimated_time': 1.0
            },
            'create_category': {
                'provides': ['category_id'],
                'requires': ['guild_context'],
                'estimated_time': 1.5
            },
            'organize_channels': {
                'provides': ['organized_channels'],
                'requires': ['guild_context', 'category_id'],
                'estimated_time': 2.0
            }
        }
        
        logger.info("ActionPlanner initialized with dependency resolution")
    
    async def create_execution_plan(self, intents: List[Dict[str, Any]], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create an execution plan from parsed intents with dependency resolution.
        
        Args:
            intents: List of intent objects from AI parsing
            context: Execution context (guild, user, etc.)
            
        Returns:
            Execution plan with ordered actions and metadata
        """
        try:
            # Convert intents to planned actions
            planned_actions = []
            action_counter = 0
            
            for intent in intents:
                intent_actions = intent.get('actions', [])
                
                for action in intent_actions:
                    action_counter += 1
                    
                    planned_action = PlannedAction(
                        action_id=f"action_{action_counter}",
                        tool_name=action.get('type', 'unknown'),
                        parameters=action.get('parameters', {}),
                        dependencies=action.get('dependencies', []),
                        priority=intent.get('priority', 1),
                        description=action.get('description', f"Execute {action.get('type', 'unknown')}"),
                        estimated_time=self.available_tools.get(action.get('type', ''), {}).get('estimated_time', 1.0)
                    )
                    
                    planned_actions.append(planned_action)
            
            # Resolve dependencies and create execution order
            execution_order = self._resolve_dependencies(planned_actions)
            
            # Calculate execution metadata
            total_time = sum(action.estimated_time for action in execution_order)
            risk_level = self._calculate_risk_level(execution_order)
            
            execution_plan = {
                'actions': [self._action_to_dict(action) for action in execution_order],
                'metadata': {
                    'total_actions': len(execution_order),
                    'estimated_time': total_time,
                    'risk_level': risk_level,
                    'requires_confirmation': risk_level in ['high', 'critical'],
                    'rollback_supported': all(action.rollback_required for action in execution_order)
                },
                'dependencies_resolved': True,
                'execution_order': [action.action_id for action in execution_order]
            }
            
            logger.info(f"Created execution plan with {len(execution_order)} actions (estimated: {total_time:.1f}s)")
            return execution_plan
            
        except Exception as e:
            logger.error(f"Error creating execution plan: {e}", exc_info=True)
            return {
                'actions': [],
                'metadata': {
                    'total_actions': 0,
                    'estimated_time': 0.0,
                    'risk_level': 'unknown',
                    'error': str(e)
                },
                'dependencies_resolved': False,
                'execution_order': []
            }
    
    def _resolve_dependencies(self, actions: List[PlannedAction]) -> List[PlannedAction]:
        """
        Resolve action dependencies and return execution order.
        
        Args:
            actions: List of planned actions
            
        Returns:
            List of actions in execution order
        """
        # Create dependency graph
        action_map = {action.action_id: action for action in actions}
        resolved = []
        remaining = actions.copy()
        
        # Simple dependency resolution (topological sort)
        max_iterations = len(actions) * 2
        iteration = 0
        
        while remaining and iteration < max_iterations:
            iteration += 1
            made_progress = False
            
            for action in remaining.copy():
                # Check if all dependencies are resolved
                dependencies_met = True
                for dep_id in action.dependencies:
                    if dep_id not in [resolved_action.action_id for resolved_action in resolved]:
                        dependencies_met = False
                        break
                
                if dependencies_met:
                    action.status = ActionStatus.READY
                    resolved.append(action)
                    remaining.remove(action)
                    made_progress = True
            
            if not made_progress:
                # Handle circular dependencies or missing dependencies
                logger.warning("Circular or missing dependencies detected, adding remaining actions")
                for action in remaining:
                    action.status = ActionStatus.READY
                    resolved.append(action)
                break
        
        # Sort by priority within dependency constraints
        return sorted(resolved, key=lambda x: (x.priority, x.estimated_time))
    
    def _calculate_risk_level(self, actions: List[PlannedAction]) -> str:
        """Calculate overall risk level for the execution plan."""
        if not actions:
            return "none"
        
        # Count high-risk operations
        destructive_actions = ['delete_channel', 'delete_role', 'kick_member', 'ban_member']
        admin_actions = ['create_role', 'modify_permissions', 'manage_guild']
        
        destructive_count = sum(1 for action in actions if action.tool_name in destructive_actions)
        admin_count = sum(1 for action in actions if action.tool_name in admin_actions)
        total_actions = len(actions)
        
        if destructive_count > 0:
            return "high"
        elif admin_count > 3 or total_actions > 10:
            return "medium"
        elif admin_count > 0 or total_actions > 5:
            return "low"
        else:
            return "minimal"
    
    def _action_to_dict(self, action: PlannedAction) -> Dict[str, Any]:
        """Convert PlannedAction to dictionary."""
        return {
            'action_id': action.action_id,
            'tool_name': action.tool_name,
            'parameters': action.parameters,
            'dependencies': action.dependencies,
            'priority': action.priority,
            'status': action.status.value,
            'estimated_time': action.estimated_time,
            'risk_level': action.risk_level,
            'rollback_required': action.rollback_required,
            'description': action.description
        }
    
    async def validate_plan(self, execution_plan: Dict[str, Any], context: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate an execution plan before execution.
        
        Args:
            execution_plan: The execution plan to validate
            context: Execution context
            
        Returns:
            Tuple of (is_valid, list_of_issues)
        """
        issues = []
        
        # Check if we have actions
        actions = execution_plan.get('actions', [])
        if not actions:
            issues.append("No actions in execution plan")
            return False, issues
        
        # Validate each action
        for action in actions:
            tool_name = action.get('tool_name')
            
            # Check if tool exists
            if tool_name not in self.available_tools:
                issues.append(f"Unknown tool: {tool_name}")
                continue
            
            # Check required parameters
            parameters = action.get('parameters', {})
            tool_info = self.available_tools[tool_name]
            
            # Basic parameter validation would go here
            # For now, just check that parameters exist
            if not parameters and tool_name != 'analyze_server':
                issues.append(f"Action {action.get('action_id')} missing parameters")
        
        # Check context requirements
        if 'guild' not in context:
            issues.append("Guild context required for execution")
        
        return len(issues) == 0, issues
    
    async def optimize_plan(self, execution_plan: Dict[str, Any]) -> Dict[str, Any]:
        """
        Optimize an execution plan for better performance.
        
        Args:
            execution_plan: The execution plan to optimize
            
        Returns:
            Optimized execution plan
        """
        actions = execution_plan.get('actions', [])
        
        # Group actions that can be parallelized
        # For now, just return the original plan
        # Future optimization: identify independent actions that can run in parallel
        
        optimized_plan = execution_plan.copy()
        optimized_plan['optimized'] = True
        
        logger.debug(f"Optimized execution plan with {len(actions)} actions")
        return optimized_plan
