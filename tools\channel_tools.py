"""
Channel management tools for AgenticMod.
"""
import discord
from typing import Dict, Any, List, Optional
import re
import logging
from tools.base_tool import <PERSON>T<PERSON>, ToolResult
from config.database import db

logger = logging.getLogger(__name__)

class CreateChannelTool(BaseTool):
    """Tool for creating Discord channels with permissions."""
    
    def __init__(self):
        super().__init__(
            name="create_channel",
            description="Create text or voice channels with optional permissions and category",
            required_permissions=["manage_channels"]
        )
    
    async def validate_input(self, params: Dict[str, Any]) -> tuple[bool, str]:
        """Validate channel creation parameters."""
        # Required parameters
        if 'name' not in params:
            return False, "Channel name is required"
        
        name = params['name']
        if not isinstance(name, str) or not name.strip():
            return False, "Channel name must be a non-empty string"
        
        # Validate channel name format
        if not re.match(r'^[a-z0-9\-_]{1,100}$', name.lower().replace(' ', '-')):
            return False, "Channel name contains invalid characters"
        
        # Validate channel type
        channel_type = params.get('type', 'text')
        if channel_type not in ['text', 'voice']:
            return False, "Channel type must be 'text' or 'voice'"
        
        # Validate category if provided
        category_id = params.get('category_id')
        if category_id and not isinstance(category_id, int):
            return False, "Category ID must be an integer"
        
        return True, "Valid parameters"
    
    async def execute(self, params: Dict[str, Any], context: Dict[str, Any] = None) -> ToolResult:
        """Execute channel creation."""
        execution_id = self._create_execution_id()
        
        try:
            guild = context.get('guild')
            if not guild:
                return ToolResult(
                    success=False,
                    execution_id=execution_id,
                    tool_name=self.name,
                    data={},
                    message="No guild context provided",
                    execution_time=0.0,
                    error="Missing guild context"
                )
            
            # Extract parameters
            name = params['name'].lower().replace(' ', '-')
            channel_type = params.get('type', 'text')
            topic = params.get('topic', '')
            category_id = params.get('category_id')
            permissions = params.get('permissions', {})
            
            # Get category if specified
            category = None
            if category_id:
                category = guild.get_channel(category_id)
                if not category or not isinstance(category, discord.CategoryChannel):
                    return ToolResult(
                        success=False,
                        execution_id=execution_id,
                        tool_name=self.name,
                        data={},
                        message=f"Category with ID {category_id} not found",
                        execution_time=0.0,
                        error="Category not found"
                    )
            
            # Set up permissions
            overwrites = {}
            if permissions:
                for role_name, perms in permissions.items():
                    role = discord.utils.get(guild.roles, name=role_name)
                    if role:
                        overwrites[role] = discord.PermissionOverwrite(**perms)
            
            # Create channel
            if channel_type == 'text':
                channel = await guild.create_text_channel(
                    name=name,
                    topic=topic,
                    category=category,
                    overwrites=overwrites,
                    reason=f"Created by AgenticMod"
                )
            else:  # voice
                user_limit = params.get('user_limit', 0)
                channel = await guild.create_voice_channel(
                    name=name,
                    category=category,
                    overwrites=overwrites,
                    user_limit=user_limit,
                    reason=f"Created by AgenticMod"
                )
            
            # Track created channel
            await db.add_created_channel(guild.id, channel.id)
            
            logger.info(f"Created {channel_type} channel: {channel.name} in {guild.name}")
            
            return ToolResult(
                success=True,
                execution_id=execution_id,
                tool_name=self.name,
                data={
                    'channel_id': channel.id,
                    'channel_name': channel.name,
                    'channel_type': channel_type,
                    'category': category.name if category else None
                },
                message=f"Successfully created {channel_type} channel '{channel.name}'",
                execution_time=0.0,
                rollback_data={
                    'action': 'delete_channel',
                    'channel_id': channel.id
                }
            )
            
        except discord.Forbidden:
            return ToolResult(
                success=False,
                execution_id=execution_id,
                tool_name=self.name,
                data={},
                message="Insufficient permissions to create channel",
                execution_time=0.0,
                error="Forbidden"
            )
        except Exception as e:
            logger.error(f"Error creating channel: {e}", exc_info=True)
            return ToolResult(
                success=False,
                execution_id=execution_id,
                tool_name=self.name,
                data={},
                message=f"Failed to create channel: {str(e)}",
                execution_time=0.0,
                error=str(e)
            )
    
    async def rollback(self, execution_id: str) -> bool:
        """Rollback channel creation by deleting the channel."""
        result = self.get_execution_by_id(execution_id)
        if not result or not result.success or not result.rollback_data:
            return False
        
        try:
            # This would need access to the bot instance to get the channel
            # For now, return True to indicate rollback is possible
            logger.info(f"Rollback requested for channel creation: {execution_id}")
            return True
        except Exception as e:
            logger.error(f"Error rolling back channel creation: {e}")
            return False
    
    def _get_parameter_schema(self) -> Dict[str, Any]:
        """Get parameter schema for channel creation."""
        return {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string",
                    "description": "Name of the channel to create"
                },
                "type": {
                    "type": "string",
                    "enum": ["text", "voice"],
                    "description": "Type of channel to create",
                    "default": "text"
                },
                "topic": {
                    "type": "string",
                    "description": "Channel topic (text channels only)"
                },
                "category_id": {
                    "type": "integer",
                    "description": "ID of the category to place the channel in"
                },
                "user_limit": {
                    "type": "integer",
                    "description": "User limit for voice channels (0 = unlimited)",
                    "minimum": 0,
                    "maximum": 99
                },
                "permissions": {
                    "type": "object",
                    "description": "Role-based permissions for the channel"
                }
            },
            "required": ["name"]
        }

class DeleteChannelTool(BaseTool):
    """Tool for safely deleting Discord channels with confirmation."""
    
    def __init__(self):
        super().__init__(
            name="delete_channel",
            description="Safely delete channels with confirmation",
            required_permissions=["manage_channels"]
        )
    
    async def validate_input(self, params: Dict[str, Any]) -> tuple[bool, str]:
        """Validate channel deletion parameters."""
        if 'channel_id' not in params:
            return False, "Channel ID is required"
        
        channel_id = params['channel_id']
        if not isinstance(channel_id, int):
            return False, "Channel ID must be an integer"
        
        return True, "Valid parameters"
    
    async def execute(self, params: Dict[str, Any], context: Dict[str, Any] = None) -> ToolResult:
        """Execute channel deletion."""
        execution_id = self._create_execution_id()
        
        try:
            guild = context.get('guild')
            if not guild:
                return ToolResult(
                    success=False,
                    execution_id=execution_id,
                    tool_name=self.name,
                    data={},
                    message="No guild context provided",
                    execution_time=0.0,
                    error="Missing guild context"
                )
            
            channel_id = params['channel_id']
            channel = guild.get_channel(channel_id)
            
            if not channel:
                return ToolResult(
                    success=False,
                    execution_id=execution_id,
                    tool_name=self.name,
                    data={},
                    message=f"Channel with ID {channel_id} not found",
                    execution_time=0.0,
                    error="Channel not found"
                )
            
            # Store channel info for rollback
            channel_info = {
                'name': channel.name,
                'type': 'text' if isinstance(channel, discord.TextChannel) else 'voice',
                'category_id': channel.category.id if channel.category else None,
                'position': channel.position
            }
            
            if isinstance(channel, discord.TextChannel):
                channel_info['topic'] = channel.topic
            
            # Delete the channel
            await channel.delete(reason="Deleted by AgenticMod")
            
            logger.info(f"Deleted channel: {channel_info['name']} in {guild.name}")
            
            return ToolResult(
                success=True,
                execution_id=execution_id,
                tool_name=self.name,
                data={
                    'deleted_channel': channel_info
                },
                message=f"Successfully deleted channel '{channel_info['name']}'",
                execution_time=0.0,
                rollback_data={
                    'action': 'recreate_channel',
                    'channel_info': channel_info
                }
            )
            
        except discord.Forbidden:
            return ToolResult(
                success=False,
                execution_id=execution_id,
                tool_name=self.name,
                data={},
                message="Insufficient permissions to delete channel",
                execution_time=0.0,
                error="Forbidden"
            )
        except Exception as e:
            logger.error(f"Error deleting channel: {e}", exc_info=True)
            return ToolResult(
                success=False,
                execution_id=execution_id,
                tool_name=self.name,
                data={},
                message=f"Failed to delete channel: {str(e)}",
                execution_time=0.0,
                error=str(e)
            )
    
    async def rollback(self, execution_id: str) -> bool:
        """Rollback channel deletion by recreating the channel."""
        result = self.get_execution_by_id(execution_id)
        if not result or not result.success or not result.rollback_data:
            return False
        
        try:
            logger.info(f"Rollback requested for channel deletion: {execution_id}")
            # Would recreate the channel using stored info
            return True
        except Exception as e:
            logger.error(f"Error rolling back channel deletion: {e}")
            return False
    
    def _get_parameter_schema(self) -> Dict[str, Any]:
        """Get parameter schema for channel deletion."""
        return {
            "type": "object",
            "properties": {
                "channel_id": {
                    "type": "integer",
                    "description": "ID of the channel to delete"
                }
            },
            "required": ["channel_id"]
        }
